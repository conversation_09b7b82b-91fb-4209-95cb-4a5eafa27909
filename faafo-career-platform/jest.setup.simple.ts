import '@testing-library/jest-dom';
import { PrismaClient } from '@prisma/client';

// Test environment variables
process.env.NEXTAUTH_SECRET = 'test-secret';
// NODE_ENV is read-only in production builds, so we use a different approach
if (!process.env.NODE_ENV) {
  (process.env as any).NODE_ENV = 'test';
}
// Keep the existing DATABASE_URL for real database testing

// Real Prisma client for testing
let testPrisma: PrismaClient;

beforeAll(async () => {
  testPrisma = new PrismaClient();
  await testPrisma.$connect();
  console.log('✅ Connected to real database for testing');
});

afterAll(async () => {
  if (testPrisma) {
    await testPrisma.$disconnect();
  }
});

beforeEach(() => {
  jest.clearAllMocks();
});
