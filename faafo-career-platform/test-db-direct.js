/**
 * Direct Database Test (No Jest)
 * Tests database connectivity without Jest interference
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: ['error', 'warn'],
});

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');
  
  try {
    // Test 1: Basic connection
    console.log('\n1. Testing basic connection...');
    await prisma.$connect();
    console.log('✅ Database connected successfully');

    // Test 2: Simple query
    console.log('\n2. Testing simple query...');
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Simple query result:', result);

    // Test 3: Count users
    console.log('\n3. Counting users...');
    const userCount = await prisma.user.count();
    console.log('✅ Total users:', userCount);

    // Test 4: List career paths
    console.log('\n4. Listing career paths...');
    const careerPaths = await prisma.careerPath.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        slug: true,
        isActive: true
      }
    });
    console.log('✅ Career paths found:', careerPaths.length);
    careerPaths.forEach(path => {
      console.log(`   - ${path.name} (${path.slug}) - Active: ${path.isActive}`);
    });

    // Test 5: Create a test user
    console.log('\n5. Creating test user...');
    const hashedPassword = await bcrypt.hash('testpassword123', 10);
    const uniqueEmail = `directtest-${Date.now()}@example.com`;

    const user = await prisma.user.create({
      data: {
        email: uniqueEmail,
        password: hashedPassword,
        name: 'Direct Test User',
      },
    });

    console.log('✅ User created:', {
      id: user.id,
      email: user.email,
      name: user.name,
      createdAt: user.createdAt
    });

    // Test 6: Clean up test user
    console.log('\n6. Cleaning up test user...');
    await prisma.user.delete({
      where: { id: user.id }
    });
    console.log('✅ Test user deleted');

    console.log('\n🎉 All database tests passed!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Database disconnected');
  }
}

// Run the test
testDatabaseConnection();
