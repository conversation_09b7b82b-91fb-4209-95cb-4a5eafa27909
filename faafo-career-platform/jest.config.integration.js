/**
 * Jest Configuration for Integration Tests
 * Uses real database connections and minimal mocking
 */

const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Integration test configuration with real database
const integrationJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.integration.js'],
  setupFiles: ['<rootDir>/jest.polyfills.js'],
  testEnvironment: 'node', // Use node environment for database tests

  // Disable automatic mocking
  automock: false,
  clearMocks: true,
  resetMocks: false,
  restoreMocks: false,
  
  // Only include integration and real database tests
  testMatch: [
    '**/__tests__/real-database.test.ts',
    '**/__tests__/simple-db-test.test.ts',
    '**/__tests__/integration-db.test.ts',
    '**/__tests__/integration/database.test.ts',
    '**/__tests__/integration/comprehensive-integration.test.ts',
    '**/__tests__/api/**/*.test.ts',
    '**/__tests__/integration/**/real-*.test.ts',
  ],
  
  // Minimal module name mapping - avoid mocking Prisma
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    // Handle CSS imports
    '^.+\\.module\\.(css|sass|scss)$': 'identity-obj-proxy',
    '^.+\\.(css|sass|scss)$': 'identity-obj-proxy',
    // Handle image imports
    '^.+\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i': '<rootDir>/__mocks__/fileMock.js',
  },
  
  collectCoverageFrom: [
    'src/app/api/**/*.{ts,tsx}',
    'src/lib/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!**/__tests__/**',
    '!**/node_modules/**',
  ],
  
  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: 'coverage/integration',
  
  testTimeout: 30000, // Longer timeout for database operations
  
  // Handle ES modules but don't transform Prisma
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        jsx: 'react-jsx',
        module: 'esnext',
        target: 'es2020',
      },
    }],
  },
  
  // Don't ignore node_modules for Prisma
  transformIgnorePatterns: [
    'node_modules/(?!(jose|@auth|@next|next-auth|openid-client|uuid)/)',
  ],
  
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  
  testEnvironmentOptions: {
    customExportConditions: [''],
  },
  
  // Global setup and teardown for database
  globalSetup: '<rootDir>/jest.global-setup.integration.js',
  globalTeardown: '<rootDir>/jest.global-teardown.integration.js',
  
  // Verbose output for debugging
  verbose: true,
  
  // Don't clear mocks between tests for integration tests
  clearMocks: false,
  restoreMocks: false,
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(integrationJestConfig)
