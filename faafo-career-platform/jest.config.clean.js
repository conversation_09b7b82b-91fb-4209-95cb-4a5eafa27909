/**
 * Clean Jest Configuration for Real Database Tests
 * No mocking, no setup files, just pure database testing
 */

module.exports = {
  testEnvironment: 'node',
  
  // Only include specific integration tests
  testMatch: [
    '**/__tests__/integration-db.test.ts',
    '**/__tests__/api-integration.test.ts',
  ],
  
  // Minimal module name mapping
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  
  // No setup files - completely clean
  setupFiles: [],
  setupFilesAfterEnv: [],
  
  // Handle TypeScript
  preset: 'ts-jest',
  extensionsToTreatAsEsm: ['.ts'],
  globals: {
    'ts-jest': {
      useESM: true,
      tsconfig: {
        module: 'esnext',
        target: 'es2020',
      },
    },
  },
  
  // Don't transform node_modules
  transformIgnorePatterns: [
    'node_modules/(?!(jose|@auth|@next|next-auth|openid-client|uuid)/)',
  ],
  
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  
  testTimeout: 30000,
  verbose: true,
  
  // No coverage for this clean test
  collectCoverage: false,
  
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
};
