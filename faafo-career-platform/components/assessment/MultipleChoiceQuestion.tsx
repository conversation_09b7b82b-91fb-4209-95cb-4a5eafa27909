import React from 'react';

interface Option {
  value: string;
  label: string;
}

interface MultipleChoiceQuestionProps {
  questionKey: string;
  options: Option[];
  allowMultiple?: boolean;
  currentValue: string | string[] | null;
  onChange: (questionKey: string, value: string | string[]) => void;
}

const MultipleChoiceQuestion: React.FC<MultipleChoiceQuestionProps> = ({
  questionKey,
  options,
  allowMultiple = false,
  currentValue,
  onChange,
}) => {
  const handleSingleChoice = (value: string) => {
    onChange(questionKey, value);
  };

  const handleMultipleChoice = (value: string) => {
    const currentValues = Array.isArray(currentValue) ? currentValue : [];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    onChange(questionKey, newValues);
  };

  const isSelected = (value: string): boolean => {
    if (allowMultiple) {
      return Array.isArray(currentValue) && currentValue.includes(value);
    }
    return currentValue === value;
  };

  return (
    <div className="space-y-3">
      {options.map((option) => (
        <label
          key={option.value}
          className={`
            flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-all duration-200
            hover:bg-gray-50 dark:hover:bg-gray-700
            ${isSelected(option.value)
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400'
              : 'border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800'
            }
          `}
        >
          <input
            type={allowMultiple ? 'checkbox' : 'radio'}
            name={allowMultiple ? `${questionKey}[]` : questionKey}
            value={option.value}
            checked={isSelected(option.value)}
            onChange={() => 
              allowMultiple 
                ? handleMultipleChoice(option.value)
                : handleSingleChoice(option.value)
            }
            className={`
              mt-0.5 h-4 w-4 border-gray-300 focus:ring-2 focus:ring-blue-500
              ${allowMultiple 
                ? 'rounded text-blue-600' 
                : 'text-blue-600'
              }
              dark:border-gray-600 dark:bg-gray-700 dark:focus:ring-blue-600
            `}
          />
          <div className="flex-1">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {option.label}
            </span>
          </div>
        </label>
      ))}
      
      {allowMultiple && (
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          You can select multiple options
        </div>
      )}
    </div>
  );
};

export default MultipleChoiceQuestion;
