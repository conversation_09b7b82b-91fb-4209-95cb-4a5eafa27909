import React from 'react';

interface QuestionWrapperProps {
  questionKey: string;
  questionText: string;
  description?: string;
  required?: boolean;
  children: React.ReactNode;
  error?: string;
}

const QuestionWrapper: React.FC<QuestionWrapperProps> = ({
  questionKey,
  questionText,
  description,
  required = false,
  children,
  error,
}) => {
  return (
    <div className="space-y-4 p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="space-y-2">
        <label 
          htmlFor={questionKey}
          className="block text-lg font-medium text-gray-900 dark:text-gray-100"
        >
          {questionText}
          {required && (
            <span className="text-red-500 ml-1" aria-label="Required">*</span>
          )}
        </label>
        
        {description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
            {description}
          </p>
        )}
      </div>

      <div className="space-y-2">
        {children}
        
        {error && (
          <div className="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded-md">
            {error}
          </div>
        )}
      </div>
    </div>
  );
};

export default QuestionWrapper;
