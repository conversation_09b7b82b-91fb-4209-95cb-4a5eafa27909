'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import GoalCard from './GoalCard';
import GoalForm, { GoalFormData } from './GoalForm';
import { UserGoal, GoalType, GoalCategory, GoalStatus } from '@prisma/client';
import { Plus, Search, Filter, Target, TrendingUp } from 'lucide-react';

interface GoalListProps {
  userId?: string;
  isOwner?: boolean;
  showCreateButton?: boolean;
}

interface GoalFilters {
  search: string;
  type: GoalType | 'ALL';
  category: GoalCategory | 'ALL';
  status: GoalStatus | 'ALL';
}

export default function GoalList({ 
  userId, 
  isOwner = false, 
  showCreateButton = true 
}: GoalListProps) {
  const [goals, setGoals] = useState<UserGoal[]>([]);
  const [filteredGoals, setFilteredGoals] = useState<UserGoal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<UserGoal | null>(null);
  const [filters, setFilters] = useState<GoalFilters>({
    search: '',
    type: 'ALL',
    category: 'ALL',
    status: 'ALL',
  });

  // Fetch goals
  useEffect(() => {
    fetchGoals();
  }, [userId]);

  // Apply filters
  useEffect(() => {
    applyFilters();
  }, [goals, filters]);

  const fetchGoals = async () => {
    try {
      setIsLoading(true);
      const url = userId ? `/api/goals?userId=${userId}` : '/api/goals';
      const response = await fetch(url);
      
      if (response.ok) {
        const data = await response.json();
        setGoals(data.goals || []);
      } else {
        console.error('Failed to fetch goals');
      }
    } catch (error) {
      console.error('Error fetching goals:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...goals];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(goal =>
        goal.title.toLowerCase().includes(searchLower) ||
        goal.description?.toLowerCase().includes(searchLower)
      );
    }

    // Type filter
    if (filters.type !== 'ALL') {
      filtered = filtered.filter(goal => goal.type === filters.type);
    }

    // Category filter
    if (filters.category !== 'ALL') {
      filtered = filtered.filter(goal => goal.category === filters.category);
    }

    // Status filter
    if (filters.status !== 'ALL') {
      filtered = filtered.filter(goal => goal.status === filters.status);
    }

    setFilteredGoals(filtered);
  };

  const handleCreateGoal = async (goalData: GoalFormData) => {
    try {
      const response = await fetch('/api/goals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(goalData),
      });

      if (response.ok) {
        await fetchGoals();
        setShowForm(false);
      } else {
        throw new Error('Failed to create goal');
      }
    } catch (error) {
      console.error('Error creating goal:', error);
      throw error;
    }
  };

  const handleUpdateGoal = async (goalData: GoalFormData) => {
    if (!editingGoal) return;

    try {
      const response = await fetch(`/api/goals`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: editingGoal.id, ...goalData }),
      });

      if (response.ok) {
        await fetchGoals();
        setEditingGoal(null);
        setShowForm(false);
      } else {
        throw new Error('Failed to update goal');
      }
    } catch (error) {
      console.error('Error updating goal:', error);
      throw error;
    }
  };

  const handleDeleteGoal = async (goalId: string) => {
    if (!confirm('Are you sure you want to delete this goal?')) return;

    try {
      const response = await fetch('/api/goals', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: goalId }),
      });

      if (response.ok) {
        await fetchGoals();
      } else {
        throw new Error('Failed to delete goal');
      }
    } catch (error) {
      console.error('Error deleting goal:', error);
    }
  };

  const handleUpdateProgress = async (goalId: string, newValue: number) => {
    try {
      const response = await fetch('/api/goals', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: goalId, currentValue: newValue }),
      });

      if (response.ok) {
        await fetchGoals();
      } else {
        throw new Error('Failed to update progress');
      }
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  const handleEditGoal = (goal: UserGoal) => {
    setEditingGoal(goal);
    setShowForm(true);
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setEditingGoal(null);
  };

  const getGoalStats = () => {
    const total = goals.length;
    const completed = goals.filter(g => g.status === 'COMPLETED').length;
    const active = goals.filter(g => g.status === 'ACTIVE').length;
    const avgProgress = goals.length > 0 
      ? goals.reduce((sum, g) => sum + (g.currentValue / g.targetValue), 0) / goals.length * 100
      : 0;

    return { total, completed, active, avgProgress };
  };

  const stats = getGoalStats();

  if (showForm) {
    return (
      <GoalForm
        goal={editingGoal}
        onSubmit={editingGoal ? handleUpdateGoal : handleCreateGoal}
        onCancel={handleCancelForm}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Target className="h-6 w-6" />
            {isOwner ? 'My Goals' : 'Goals'}
          </h2>
          <p className="text-gray-600 mt-1">
            Track your learning and career objectives
          </p>
        </div>
        
        {isOwner && showCreateButton && (
          <Button onClick={() => setShowForm(true)} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create Goal
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      {goals.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Goals</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Active</p>
                  <p className="text-2xl font-bold">{stats.active}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <span className="text-blue-600">✓</span>
                <div>
                  <p className="text-sm text-gray-600">Completed</p>
                  <p className="text-2xl font-bold">{stats.completed}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <span className="text-purple-600">📊</span>
                <div>
                  <p className="text-sm text-gray-600">Avg Progress</p>
                  <p className="text-2xl font-bold">{stats.avgProgress.toFixed(0)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search goals..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pl-10"
              />
            </div>
            
            <Select
              value={filters.type}
              onValueChange={(value: GoalType | 'ALL') => setFilters(prev => ({ ...prev, type: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Types</SelectItem>
                <SelectItem value="DAILY">Daily</SelectItem>
                <SelectItem value="WEEKLY">Weekly</SelectItem>
                <SelectItem value="MONTHLY">Monthly</SelectItem>
                <SelectItem value="YEARLY">Yearly</SelectItem>
                <SelectItem value="CUSTOM">Custom</SelectItem>
              </SelectContent>
            </Select>
            
            <Select
              value={filters.category}
              onValueChange={(value: GoalCategory | 'ALL') => setFilters(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Categories</SelectItem>
                <SelectItem value="LEARNING_RESOURCES">Learning Resources</SelectItem>
                <SelectItem value="SKILLS">Skills</SelectItem>
                <SelectItem value="CERTIFICATIONS">Certifications</SelectItem>
                <SelectItem value="PROJECTS">Projects</SelectItem>
                <SelectItem value="CAREER_MILESTONES">Career Milestones</SelectItem>
                <SelectItem value="NETWORKING">Networking</SelectItem>
              </SelectContent>
            </Select>
            
            <Select
              value={filters.status}
              onValueChange={(value: GoalStatus | 'ALL') => setFilters(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Statuses</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="PAUSED">Paused</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Goals List */}
      {isLoading ? (
        <div className="text-center py-8">
          <p>Loading goals...</p>
        </div>
      ) : filteredGoals.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {goals.length === 0 ? 'No goals yet' : 'No goals match your filters'}
            </h3>
            <p className="text-gray-600 mb-4">
              {goals.length === 0 
                ? 'Start by creating your first learning goal'
                : 'Try adjusting your search or filter criteria'
              }
            </p>
            {isOwner && goals.length === 0 && (
              <Button onClick={() => setShowForm(true)} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create Your First Goal
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredGoals.map((goal) => (
            <GoalCard
              key={goal.id}
              goal={goal}
              onEdit={isOwner ? handleEditGoal : undefined}
              onDelete={isOwner ? handleDeleteGoal : undefined}
              onUpdateProgress={isOwner ? handleUpdateProgress : undefined}
              isOwner={isOwner}
            />
          ))}
        </div>
      )}
    </div>
  );
}
