"use client";

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface EmailVerificationBannerProps {
  onVerificationSent?: () => void;
}

const EmailVerificationBanner: React.FC<EmailVerificationBannerProps> = ({ onVerificationSent }) => {
  const { data: session, status } = useSession();
  const [isVisible, setIsVisible] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [message, setMessage] = useState('');
  const [isVerified, setIsVerified] = useState(true);

  useEffect(() => {
    const checkVerificationStatus = async () => {
      if (status === 'authenticated' && session?.user?.email) {
        try {
          // Check if user's email is verified by making a request to our API
          const response = await fetch('/api/auth/verification-status');
          if (response.ok) {
            const data = await response.json();
            setIsVerified(data.isVerified);
            setIsVisible(!data.isVerified);
          }
        } catch (error) {
          console.error('Error checking verification status:', error);
        }
      }
    };

    checkVerificationStatus();
  }, [session, status]);

  const handleResendVerification = async () => {
    if (!session?.user?.email) return;

    setIsResending(true);
    setMessage('');

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: session.user.email }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Verification email sent! Please check your inbox.');
        onVerificationSent?.();
      } else {
        setMessage(data.error || 'Failed to send verification email.');
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      setMessage('An unexpected error occurred.');
    } finally {
      setIsResending(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
  };

  if (!isVisible || isVerified || status !== 'authenticated') {
    return null;
  }

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm text-yellow-700 dark:text-yellow-200">
            <strong>Email verification required:</strong> Please verify your email address to access all features.
            {message && (
              <span className={`block mt-1 ${message.includes('sent') ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {message}
              </span>
            )}
          </p>
          <div className="mt-2 flex space-x-3">
            <button
              onClick={handleResendVerification}
              disabled={isResending}
              className="text-sm text-yellow-800 dark:text-yellow-200 underline hover:text-yellow-600 dark:hover:text-yellow-400 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isResending ? 'Sending...' : 'Resend verification email'}
            </button>
            <button
              onClick={handleDismiss}
              className="text-sm text-yellow-800 dark:text-yellow-200 underline hover:text-yellow-600 dark:hover:text-yellow-400"
            >
              Dismiss
            </button>
          </div>
        </div>
        <div className="flex-shrink-0 ml-3">
          <button
            onClick={handleDismiss}
            className="inline-flex text-yellow-400 hover:text-yellow-600 dark:hover:text-yellow-200"
          >
            <span className="sr-only">Dismiss</span>
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationBanner;
