import React from "react";
import { Card } from "@/components/ui/card";
import { CheckCircle, XCircle, ListChecks, ExternalLink } from "lucide-react";
import { cn } from "@/lib/utils";

interface CareerPathCardProps {
  title: string;
  overview: string;
  pros: string[] | undefined;
  cons: string[] | undefined;
  actionableSteps: string[] | undefined;
  className?: string;
  onClick?: () => void;
}

const CareerPathCard = ({
  title,
  overview,
  pros,
  cons,
  actionableSteps,
  className,
  onClick,
}: CareerPathCardProps) => {
  return (
    <Card
      className={cn(
        "border w-full rounded-md overflow-hidden bg-background border-border p-5 transition-all hover:shadow-md",
        className
      )}
      onClick={onClick}
    >
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-lg font-bold mb-1 text-foreground">{title}</h3>
          </div>
        </div>

        <p className="text-wrap text-sm text-foreground/60">{overview}</p>

        <div className="pt-2">
          <h4 className="text-sm font-medium mb-2 text-foreground flex items-center">
            <CheckCircle className="h-4 w-4 mr-2 text-green-500" /> Pros
          </h4>
          <ul className="list-none pl-0 space-y-1">
            {(pros ?? []).map((pro, index) => (
              <li key={index} className="flex items-start">
                 <CheckCircle className="h-3 w-3 mr-2 mt-0.5 text-green-500 flex-shrink-0" />
                <span className="text-foreground">{pro}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="pt-2">
          <h4 className="text-sm font-medium mb-2 text-foreground flex items-center">
            <XCircle className="h-4 w-4 mr-2 text-red-500" /> Cons
          </h4>
          <ul className="list-none pl-0 space-y-1">
            {(cons ?? []).map((con, index) => (
              <li key={index} className="flex items-start">
                <XCircle className="h-3 w-3 mr-2 mt-0.5 text-red-500 flex-shrink-0" />
                <span className="text-foreground">{con}</span>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="pt-2">
          <h4 className="text-sm font-medium mb-2 text-foreground flex items-center">
            <ListChecks className="h-4 w-4 mr-2 text-blue-500" /> Actionable Steps
          </h4>
          <ul className="list-none pl-0 space-y-1">
            {(actionableSteps ?? []).map((step, index) => (
              <li key={index} className="flex items-start">
                <ListChecks className="h-3 w-3 mr-2 mt-0.5 text-blue-500 flex-shrink-0" />
               <span className="text-foreground">{step}</span>
              </li>
            ))}
          </ul>
        </div>

        {onClick && (
          <div className="pt-2 flex items-center text-sm font-medium text-primary hover:underline cursor-pointer">
            <span className="mr-1">Learn more</span>
            <ExternalLink className="h-4 w-4" />
          </div>
        )}
      </div>
    </Card>
  );
};

export default CareerPathCard; 