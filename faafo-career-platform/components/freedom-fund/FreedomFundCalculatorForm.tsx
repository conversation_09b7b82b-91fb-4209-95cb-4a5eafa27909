'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Submit<PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Calculator, DollarSign, Calendar, Target, TrendingUp, AlertCircle, CheckCircle2 } from 'lucide-react';

interface FreedomFundFormInput {
  monthlyExpenses: number;
  coverageMonths: number;
  currentSavings?: number;
  monthlyContribution?: number;
  adjustForInflation?: boolean;
}

interface FreedomFundCalculatorFormProps {
  onSubmit: (data: FreedomFundFormInput) => void;
  // Potentially pass initial data if loading existing record
  initialData?: Partial<FreedomFundFormInput>;
}

const coverageOptions = [
  { value: 3, label: '3 months', description: 'Basic emergency fund' },
  { value: 6, label: '6 months', description: 'Recommended minimum' },
  { value: 9, label: '9 months', description: 'Enhanced security' },
  { value: 12, label: '12 months', description: 'Maximum protection' }
];

const inflationRate = 0.03; // 3% annual inflation

export default function FreedomFundCalculatorForm({
  onSubmit,
  initialData = {},
}: FreedomFundCalculatorFormProps) {
  const { control, handleSubmit, watch, formState: { errors } } = useForm<FreedomFundFormInput>({
    defaultValues: {
      monthlyExpenses: initialData.monthlyExpenses,
      coverageMonths: initialData.coverageMonths ?? 6,
      currentSavings: initialData.currentSavings,
      monthlyContribution: initialData.monthlyContribution,
      adjustForInflation: initialData.adjustForInflation ?? false,
    },
    mode: "onBlur",
  });

  // Watch form values for real-time calculations
  const watchedValues = watch();
  const [previewCalculations, setPreviewCalculations] = useState<{
    targetAmount: number;
    monthsToGoal: number;
    recommendedMonthly: number;
    inflationAdjusted: number;
  } | null>(null);

  // Real-time calculation effect
  useEffect(() => {
    const { monthlyExpenses, coverageMonths, currentSavings, monthlyContribution, adjustForInflation } = watchedValues;

    if (monthlyExpenses && coverageMonths) {
      const baseTarget = monthlyExpenses * coverageMonths;
      const inflationAdjustedTarget = adjustForInflation ? baseTarget * (1 + inflationRate) : baseTarget;
      const remaining = Math.max(inflationAdjustedTarget - (currentSavings || 0), 0);
      const monthsToGoal = monthlyContribution && monthlyContribution > 0 ? Math.ceil(remaining / monthlyContribution) : 0;
      const recommendedMonthly = remaining > 0 ? Math.ceil(remaining / 12) : 0; // Assume 1-year goal

      setPreviewCalculations({
        targetAmount: inflationAdjustedTarget,
        monthsToGoal,
        recommendedMonthly,
        inflationAdjusted: inflationAdjustedTarget - baseTarget,
      });
    } else {
      setPreviewCalculations(null);
    }
  }, [watchedValues]);

  const handleFormSubmit: SubmitHandler<FreedomFundFormInput> = (data) => {
    // Convert numeric string inputs to numbers before submitting
    const numericData = {
      ...data,
      monthlyExpenses: parseFloat(String(data.monthlyExpenses)),
      currentSavings: data.currentSavings ? parseFloat(String(data.currentSavings)) : undefined,
      monthlyContribution: data.monthlyContribution ? parseFloat(String(data.monthlyContribution)) : undefined,
      coverageMonths: parseInt(String(data.coverageMonths), 10),
    };
    onSubmit(numericData);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5 text-primary" />
            Freedom Fund Calculator
          </CardTitle>
          <CardDescription>
            Calculate your emergency savings target and create a plan to achieve financial security.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            {/* Monthly Expenses Input */}
            <div className="space-y-2">
              <label htmlFor="monthlyExpenses" className="flex items-center gap-2 text-sm font-medium">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                Monthly Essential Expenses
              </label>
              <Controller
                name="monthlyExpenses"
                control={control}
                rules={{
                  required: 'Monthly expenses are required.',
                  min: { value: 0.01, message: 'Must be greater than 0.' },
                }}
                render={({ field }) => (
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">$</span>
                    <input
                      {...field}
                      type="number"
                      id="monthlyExpenses"
                      className="pl-8 w-full px-3 py-2 border border-input rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring bg-background text-foreground"
                      step="0.01"
                      placeholder="3,500"
                    />
                  </div>
                )}
              />
              {errors.monthlyExpenses && (
                <p className="flex items-center gap-1 text-sm text-destructive" role="alert">
                  <AlertCircle className="h-3 w-3" />
                  {errors.monthlyExpenses.message}
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                Include rent, utilities, groceries, insurance, and other essential monthly costs.
              </p>
            </div>

            {/* Coverage Months Selection */}
            <div className="space-y-3">
              <label htmlFor="coverageMonths" className="flex items-center gap-2 text-sm font-medium">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                Desired Coverage Period
              </label>
              <Controller
                name="coverageMonths"
                control={control}
                defaultValue={6}
                rules={{ required: 'Please select coverage months.' }}
                render={({ field }) => (
                  <div className="grid grid-cols-2 gap-3">
                    {coverageOptions.map((option) => (
                      <label
                        key={option.value}
                        className={`relative flex flex-col p-4 border rounded-lg cursor-pointer transition-all hover:border-primary/50 ${
                          field.value === option.value
                            ? 'border-primary bg-primary/5 ring-1 ring-primary/20'
                            : 'border-border'
                        }`}
                      >
                        <input
                          type="radio"
                          value={option.value}
                          checked={field.value === option.value}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                          className="sr-only"
                        />
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{option.label}</span>
                          {field.value === option.value && (
                            <CheckCircle2 className="h-4 w-4 text-primary" />
                          )}
                        </div>
                        <span className="text-xs text-muted-foreground mt-1">{option.description}</span>
                      </label>
                    ))}
                  </div>
                )}
              />
              {errors.coverageMonths && (
                <p className="flex items-center gap-1 text-sm text-destructive" role="alert">
                  <AlertCircle className="h-3 w-3" />
                  {errors.coverageMonths.message}
                </p>
              )}
            </div>

            {/* Current Savings Input */}
            <div className="space-y-2">
              <label htmlFor="currentSavings" className="flex items-center gap-2 text-sm font-medium">
                <Target className="h-4 w-4 text-muted-foreground" />
                Current Emergency Savings (Optional)
              </label>
              <Controller
                name="currentSavings"
                control={control}
                rules={{
                  min: { value: 0, message: 'Cannot be negative.' },
                }}
                render={({ field }) => (
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">$</span>
                    <input
                      {...field}
                      value={field.value === null || field.value === undefined ? '' : field.value}
                      type="number"
                      id="currentSavings"
                      className="pl-8 w-full px-3 py-2 border border-input rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring bg-background text-foreground"
                      step="0.01"
                      placeholder="1,500"
                    />
                  </div>
                )}
              />
              {errors.currentSavings && (
                <p className="flex items-center gap-1 text-sm text-destructive" role="alert">
                  <AlertCircle className="h-3 w-3" />
                  {errors.currentSavings.message}
                </p>
              )}
            </div>

            {/* Monthly Contribution Input */}
            <div className="space-y-2">
              <label htmlFor="monthlyContribution" className="flex items-center gap-2 text-sm font-medium">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                Monthly Savings Contribution (Optional)
              </label>
              <Controller
                name="monthlyContribution"
                control={control}
                rules={{
                  min: { value: 0, message: 'Cannot be negative.' },
                }}
                render={({ field }) => (
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">$</span>
                    <input
                      {...field}
                      value={field.value === null || field.value === undefined ? '' : field.value}
                      type="number"
                      id="monthlyContribution"
                      className="pl-8 w-full px-3 py-2 border border-input rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring bg-background text-foreground"
                      step="0.01"
                      placeholder="500"
                    />
                  </div>
                )}
              />
              <p className="text-xs text-muted-foreground">
                How much can you save toward your freedom fund each month?
              </p>
            </div>

            {/* Inflation Adjustment Toggle */}
            <div className="space-y-2">
              <Controller
                name="adjustForInflation"
                control={control}
                render={({ field }) => (
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={field.value || false}
                      onChange={field.onChange}
                      className="w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2"
                    />
                    <div>
                      <span className="text-sm font-medium">Adjust for inflation</span>
                      <p className="text-xs text-muted-foreground">
                        Add 3% to account for rising costs over time
                      </p>
                    </div>
                  </label>
                )}
              />
            </div>

            <Button type="submit" className="w-full" size="lg">
              <Calculator className="h-4 w-4 mr-2" />
              Calculate & Save Target
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Real-time Preview */}
      {previewCalculations && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Preview Calculations</CardTitle>
            <CardDescription>
              Live preview based on your inputs
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Target Amount</span>
                </div>
                <p className="text-2xl font-bold text-primary">
                  ${previewCalculations.targetAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                  })}
                </p>
                {previewCalculations.inflationAdjusted > 0 && (
                  <p className="text-xs text-muted-foreground mt-1">
                    +${previewCalculations.inflationAdjusted.toLocaleString(undefined, {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    })} inflation adjustment
                  </p>
                )}
              </div>

              <div className="p-4 bg-secondary/50 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="h-4 w-4 text-secondary-foreground" />
                  <span className="text-sm font-medium">Recommended Monthly</span>
                </div>
                <p className="text-2xl font-bold">
                  ${previewCalculations.recommendedMonthly.toLocaleString()}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  To reach goal in 12 months
                </p>
              </div>
            </div>

            {previewCalculations.monthsToGoal > 0 && watchedValues.monthlyContribution && (
              <div className="p-4 bg-accent/50 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-accent-foreground" />
                  <span className="text-sm font-medium">Time to Goal</span>
                </div>
                <p className="text-xl font-bold">
                  {previewCalculations.monthsToGoal} months
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  At ${watchedValues.monthlyContribution}/month
                </p>
              </div>
            )}

            {watchedValues.currentSavings && previewCalculations.targetAmount > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress to Goal</span>
                  <span>
                    {Math.min(((watchedValues.currentSavings || 0) / previewCalculations.targetAmount) * 100, 100).toFixed(0)}%
                  </span>
                </div>
                <Progress
                  value={Math.min(((watchedValues.currentSavings || 0) / previewCalculations.targetAmount) * 100, 100)}
                  className="h-2"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>
                    ${(watchedValues.currentSavings || 0).toLocaleString()}
                  </span>
                  <span>
                    ${Math.max(previewCalculations.targetAmount - (watchedValues.currentSavings || 0), 0).toLocaleString()} remaining
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}