'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface OnboardingStepProps {
  title: string;
  description: string;
  children: React.ReactNode;
}

function OnboardingStep({ title, description, children }: OnboardingStepProps) {
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-2xl">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  );
}

export default function OnboardingFlow() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3; // Welcome, Assessment, Freedom Fund (Example)

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      // Onboarding completed, navigate to dashboard or home
      router.push('/dashboard');
    }
  };

  const handleStartAssessment = () => {
    router.push('/assessment');
  };

  const handleSkip = () => {
    router.push('/dashboard'); // Or a less intrusive path
  };

  const getStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <OnboardingStep
            title="Welcome to FAAFO!"
            description="Your journey to a fulfilling career starts here. Let&apos;s get you set up."
          >
            <p className="mb-6">Complete a quick assessment to personalize your career recommendations and unlock your financial freedom path.</p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleSkip}>Skip</Button>
              <Button onClick={handleNext}>Next</Button>
            </div>
          </OnboardingStep>
        );
      case 2:
        return (
          <OnboardingStep
            title="Your Career Blueprint"
            description="Discover career paths tailored to your unique strengths and aspirations."
          >
            <p className="mb-6">Take our comprehensive self-assessment to identify your core skills, interests, and values.</p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setCurrentStep(currentStep - 1)}>Back</Button>
              <Button onClick={handleStartAssessment}>Start Assessment</Button>
            </div>
          </OnboardingStep>
        );
      case 3:
        return (
          <OnboardingStep
            title="Build Your Freedom Fund"
            description="Plan your finances to achieve independence and pursue your dream career."
          >
            <p className="mb-6">Utilize our tools to set financial goals, track your savings, and secure your future.</p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setCurrentStep(currentStep - 1)}>Back</Button>
              <Button onClick={handleNext}>Finish Onboarding</Button>
            </div>
          </OnboardingStep>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-80px)] bg-gray-50 dark:bg-gray-950 px-4">
      <div className="w-full max-w-md mb-6">
        <Progress value={(currentStep / totalSteps) * 100} className="w-full" />
        <p className="text-center text-sm text-muted-foreground mt-2">Step {currentStep} of {totalSteps}</p>
      </div>
      {getStepContent()}
    </div>
  );
} 