'use client';

import React from 'react';
import Image from 'next/image';
import { User, Calendar, MessageSquare, Star, Award, TrendingUp } from 'lucide-react';

interface ForumUserProfileProps {
  user: {
    id: string;
    name?: string;
    email: string;
    profile?: {
      profilePictureUrl?: string;
      forumReputation?: number;
      forumPostCount?: number;
      currentCareerPath?: string;
      progressLevel?: string;
      achievements?: any;
    };
  };
  joinDate?: string;
  showFullProfile?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export default function ForumUserProfile({ 
  user, 
  joinDate, 
  showFullProfile = false, 
  size = 'medium' 
}: ForumUserProfileProps) {
  const displayName = user.name || user.email.split('@')[0];
  const reputation = user.profile?.forumReputation || 0;
  const postCount = user.profile?.forumPostCount || 0;
  
  // Calculate user level based on reputation
  const getUserLevel = (rep: number) => {
    if (rep < 10) return { level: 'Newcomer', color: 'text-gray-500', bgColor: 'bg-gray-100' };
    if (rep < 50) return { level: 'Member', color: 'text-blue-600', bgColor: 'bg-blue-100' };
    if (rep < 100) return { level: 'Active Member', color: 'text-green-600', bgColor: 'bg-green-100' };
    if (rep < 250) return { level: 'Contributor', color: 'text-purple-600', bgColor: 'bg-purple-100' };
    if (rep < 500) return { level: 'Expert', color: 'text-orange-600', bgColor: 'bg-orange-100' };
    return { level: 'Guru', color: 'text-red-600', bgColor: 'bg-red-100' };
  };

  const userLevel = getUserLevel(reputation);

  const formatJoinDate = (dateString?: string) => {
    if (!dateString) return 'Recently joined';
    const date = new Date(dateString);
    return `Joined ${date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}`;
  };

  if (size === 'small') {
    return (
      <div className="flex items-center gap-2">
        <div className="relative">
          {user.profile?.profilePictureUrl ? (
            <Image
              src={user.profile.profilePictureUrl}
              alt={displayName}
              width={24}
              height={24}
              className="rounded-full"
            />
          ) : (
            <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <User className="h-3 w-3 text-gray-600 dark:text-gray-300" />
            </div>
          )}
        </div>
        <span className="text-sm font-medium text-gray-900 dark:text-white">
          {displayName}
        </span>
        {reputation > 0 && (
          <span className={`text-xs px-1.5 py-0.5 rounded ${userLevel.bgColor} ${userLevel.color}`}>
            {reputation}
          </span>
        )}
      </div>
    );
  }

  if (size === 'medium') {
    return (
      <div className="flex items-start gap-3">
        <div className="relative">
          {user.profile?.profilePictureUrl ? (
            <Image
              src={user.profile.profilePictureUrl}
              alt={displayName}
              width={40}
              height={40}
              className="rounded-full"
            />
          ) : (
            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            </div>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-gray-900 dark:text-white truncate">
              {displayName}
            </h4>
            <span className={`text-xs px-2 py-1 rounded-full ${userLevel.bgColor} ${userLevel.color}`}>
              {userLevel.level}
            </span>
          </div>
          
          <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3" />
              <span>{reputation} reputation</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageSquare className="h-3 w-3" />
              <span>{postCount} posts</span>
            </div>
          </div>
          
          {user.profile?.currentCareerPath && (
            <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              {user.profile.currentCareerPath}
              {user.profile.progressLevel && ` • ${user.profile.progressLevel}`}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Large size - full profile
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-start gap-4">
        <div className="relative">
          {user.profile?.profilePictureUrl ? (
            <Image
              src={user.profile.profilePictureUrl}
              alt={displayName}
              width={64}
              height={64}
              className="rounded-full"
            />
          ) : (
            <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 text-gray-600 dark:text-gray-300" />
            </div>
          )}
        </div>
        
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              {displayName}
            </h3>
            <span className={`px-3 py-1 rounded-full text-sm ${userLevel.bgColor} ${userLevel.color}`}>
              {userLevel.level}
            </span>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <Star className="h-4 w-4" />
              <span>{reputation} reputation</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <MessageSquare className="h-4 w-4" />
              <span>{postCount} posts</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <Calendar className="h-4 w-4" />
              <span>{formatJoinDate(joinDate)}</span>
            </div>
            {user.profile?.currentCareerPath && (
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <TrendingUp className="h-4 w-4" />
                <span>{user.profile.currentCareerPath}</span>
              </div>
            )}
          </div>
          
          {user.profile?.progressLevel && (
            <div className="mb-4">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {user.profile.progressLevel}
              </span>
            </div>
          )}
          
          {/* Achievements */}
          {user.profile?.achievements && (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2 flex items-center gap-2">
                <Award className="h-4 w-4" />
                Achievements
              </h4>
              <div className="flex flex-wrap gap-2">
                {/* This would be populated with actual achievement data */}
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                  🎯 First Post
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  🚀 Active Contributor
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
