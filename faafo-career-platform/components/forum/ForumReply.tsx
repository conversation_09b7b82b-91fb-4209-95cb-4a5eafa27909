'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import ReactionButtons from './ReactionButtons';
import ReportButton from './ReportButton';
import { 
  MoreHorizontal,
  Edit,
  Trash2,
  Reply as ReplyIcon
} from 'lucide-react';
import { ForumReply as ForumReplyType, User } from '@prisma/client';

interface ForumReplyProps {
  reply: ForumReplyType & {
    author: User;
    _count?: {
      reactions: number;
    };
  };
  currentUserId?: string;
  onEdit?: (reply: ForumReplyType) => void;
  onDelete?: (replyId: string) => void;
  onReply?: (replyId: string) => void;
  className?: string;
  level?: number; // For nested replies
}

export default function ForumReply({
  reply,
  currentUserId,
  onEdit,
  onDelete,
  onReply,
  className = '',
  level = 0,
}: ForumReplyProps) {
  const [showActions, setShowActions] = useState(false);
  const isAuthor = currentUserId === reply.authorId;
  const maxNestingLevel = 3; // Limit reply nesting

  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.ceil((d.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      'day'
    );
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(reply);
    }
  };

  const handleDelete = () => {
    if (onDelete && confirm('Are you sure you want to delete this reply?')) {
      onDelete(reply.id);
    }
  };

  const handleReply = () => {
    if (onReply) {
      onReply(reply.id);
    }
  };

  return (
    <Card 
      className={`transition-all duration-200 hover:shadow-sm ${className}`}
      style={{ marginLeft: `${level * 20}px` }}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Avatar className="h-8 w-8 flex-shrink-0">
            <AvatarImage src={reply.author.image || undefined} />
            <AvatarFallback className="text-xs">
              {reply.author.name?.charAt(0) || reply.author.email?.charAt(0) || 'U'}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            {/* Reply Header */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2 text-sm">
                <span className="font-medium text-gray-900">
                  {reply.author.name || 'Anonymous'}
                </span>
                <span className="text-gray-500">•</span>
                <span className="text-gray-600">
                  {formatDate(reply.createdAt)}
                </span>
                {reply.updatedAt !== reply.createdAt && (
                  <>
                    <span className="text-gray-500">•</span>
                    <span className="text-gray-500 text-xs">
                      edited {formatDate(reply.updatedAt)}
                    </span>
                  </>
                )}
              </div>
              
              {/* Actions Menu */}
              <div className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowActions(!showActions)}
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
                
                {showActions && (
                  <div className="absolute right-0 top-6 bg-white border rounded-md shadow-lg z-10 min-w-[120px]">
                    {isAuthor && onEdit && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleEdit}
                        className="w-full justify-start text-left text-xs"
                      >
                        <Edit className="h-3 w-3 mr-2" />
                        Edit
                      </Button>
                    )}
                    
                    {isAuthor && onDelete && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleDelete}
                        className="w-full justify-start text-left text-xs text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3 mr-2" />
                        Delete
                      </Button>
                    )}
                    
                    <ReportButton
                      replyId={reply.id}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Reply Content */}
            <div className="prose prose-sm max-w-none mb-3">
              <p className="whitespace-pre-wrap text-gray-800 text-sm leading-relaxed">
                {reply.content}
              </p>
            </div>

            <Separator className="my-3" />

            {/* Action Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ReactionButtons
                  replyId={reply.id}
                  size="small"
                />
                
                {onReply && level < maxNestingLevel && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleReply}
                    className="flex items-center gap-1 text-xs h-7 px-2"
                  >
                    <ReplyIcon className="h-3 w-3" />
                    Reply
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
