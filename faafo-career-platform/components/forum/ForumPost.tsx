'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import ReactionButtons from './ReactionButtons';
import BookmarkButton from './BookmarkButton';
import ReportButton from './ReportButton';
import { 
  MessageSquare, 
  Eye, 
  Clock, 
  Pin, 
  Lock, 
  MoreHorizontal,
  Edit,
  Trash2
} from 'lucide-react';
import { ForumPost as ForumPostType, User, ForumCategory } from '@prisma/client';

interface ForumPostProps {
  post: ForumPostType & {
    author: User;
    category?: ForumCategory | null;
    _count?: {
      replies: number;
      reactions: number;
    };
  };
  currentUserId?: string;
  onEdit?: (post: ForumPostType) => void;
  onDelete?: (postId: string) => void;
  onReply?: (postId: string) => void;
  showFullContent?: boolean;
  className?: string;
}

export default function ForumPost({
  post,
  currentUserId,
  onEdit,
  onDelete,
  onReply,
  showFullContent = false,
  className = '',
}: ForumPostProps) {
  const [showActions, setShowActions] = useState(false);
  const isAuthor = currentUserId === post.authorId;
  const replyCount = post._count?.replies || 0;

  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.ceil((d.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      'day'
    );
  };

  const formatContent = (content: string) => {
    if (!showFullContent && content.length > 300) {
      return content.substring(0, 300) + '...';
    }
    return content;
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(post);
    }
  };

  const handleDelete = () => {
    if (onDelete && confirm('Are you sure you want to delete this post?')) {
      onDelete(post.id);
    }
  };

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <Avatar className="h-10 w-10">
              <AvatarImage src={post.author.image || undefined} />
              <AvatarFallback>
                {post.author.name?.charAt(0) || post.author.email?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-lg leading-tight">
                  {post.title}
                </h3>
                
                {post.isPinned && (
                  <Pin className="h-4 w-4 text-blue-600" aria-label="Pinned post" />
                )}

                {post.isLocked && (
                  <Lock className="h-4 w-4 text-red-600" aria-label="Locked post" />
                )}
              </div>
              
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span className="font-medium">{post.author.name || 'Anonymous'}</span>
                <span>•</span>
                <span>{formatDate(post.createdAt)}</span>
                
                {post.category && (
                  <>
                    <span>•</span>
                    <Badge variant="secondary" className="text-xs">
                      {post.category.name}
                    </Badge>
                  </>
                )}
              </div>
            </div>
          </div>
          
          {/* Actions Menu */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowActions(!showActions)}
              className="h-8 w-8 p-0"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
            
            {showActions && (
              <div className="absolute right-0 top-8 bg-white border rounded-md shadow-lg z-10 min-w-[120px]">
                {isAuthor && onEdit && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleEdit}
                    className="w-full justify-start text-left"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                )}
                
                {isAuthor && onDelete && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDelete}
                    className="w-full justify-start text-left text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                )}
                
                <ReportButton
                  postId={post.id}
                />
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Post Content */}
        <div className="prose prose-sm max-w-none">
          <p className="whitespace-pre-wrap text-gray-800">
            {formatContent(post.content)}
          </p>
          
          {!showFullContent && post.content.length > 300 && (
            <Button variant="link" className="p-0 h-auto text-blue-600">
              Read more
            </Button>
          )}
        </div>

        {/* Tags */}
        {post.tags && Array.isArray(post.tags) && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {(post.tags as string[]).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                #{tag}
              </Badge>
            ))}
          </div>
        )}

        <Separator />

        {/* Post Stats */}
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>{post.viewCount} views</span>
            </div>
            
            <div className="flex items-center gap-1">
              <MessageSquare className="h-4 w-4" />
              <span>{replyCount} replies</span>
            </div>
            
            {post.updatedAt !== post.createdAt && (
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>Updated {formatDate(post.updatedAt)}</span>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ReactionButtons
              postId={post.id}
            />
            
            {onReply && !post.isLocked && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onReply(post.id)}
                className="flex items-center gap-1"
              >
                <MessageSquare className="h-4 w-4" />
                Reply
              </Button>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <BookmarkButton
              postId={post.id}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
