"use client";

import * as React from "react";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";

function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Types
interface StatItem {
  name: string;
  value: string;
  change: string;
  changeType: "positive" | "negative" | "neutral";
  href: string;
}

interface AnalyticsDashboardProps {
  className?: string;
}

// Mock data
const statsData: StatItem[] = [
  {
    name: "Monthly recurring revenue",
    value: "$34.1K",
    change: "+6.1%",
    changeType: "positive",
    href: "#",
  },
  {
    name: "Active users",
    value: "500.1K",
    change: "+19.2%",
    changeType: "positive",
    href: "#",
  },
  {
    name: "Conversion rate",
    value: "11.3%",
    change: "-1.2%",
    changeType: "negative",
    href: "#",
  },
  {
    name: "Avg. session duration",
    value: "2m 45s",
    change: "+0.8%",
    changeType: "positive",
    href: "#",
  },
];

// Stats Card Component
function StatsCard({ item }: { item: StatItem }) {
  return (
    <Card className="p-0 gap-0">
      <div className="p-6">
        <div className="flex items-start justify-between space-x-2">
          <span className="truncate text-sm text-muted-foreground">
            {item.name}
          </span>
          <span
            className={cn(
              "text-sm font-medium",
              item.changeType === "positive"
                ? "text-emerald-700 dark:text-emerald-500"
                : item.changeType === "negative"
                ? "text-red-700 dark:text-red-500"
                : "text-muted-foreground"
            )}
          >
            {item.change}
          </span>
        </div>
        <div className="mt-1 text-3xl font-semibold text-foreground">
          {item.value}
        </div>
      </div>
      <div className="flex justify-end border-t border-border !p-0">
        <a
          href={item.href}
          className="px-6 py-3 text-sm font-medium text-primary hover:text-primary/90"
        >
          View more →
        </a>
      </div>
    </Card>
  );
}

// Chart Component
function ChartSection() {
  return (
    <Card className="col-span-full lg:col-span-2">
      <div className="p-6">
        <Tabs defaultValue="revenue">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Performance Metrics</h3>
            <TabsList>
              <TabsTrigger value="revenue" className="flex items-center gap-1">
                <LineChart className="h-4 w-4" />
                <span>Revenue</span>
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-1">
                <BarChart className="h-4 w-4" />
                <span>Users</span>
              </TabsTrigger>
              <TabsTrigger value="devices" className="flex items-center gap-1">
                <PieChart className="h-4 w-4" />
                <span>Devices</span>
              </TabsTrigger>
            </TabsList>
          </div>
          
          <TabsContent value="revenue" className="h-[300px] mt-0">
            <div className="h-full w-full flex items-center justify-center text-muted-foreground">
              Revenue Chart (Line Chart visualization would render here)
            </div>
          </TabsContent>
          
          <TabsContent value="users" className="h-[300px] mt-0">
            <div className="h-full w-full flex items-center justify-center text-muted-foreground">
              User Activity Chart (Bar Chart visualization would render here)
            </div>
          </TabsContent>
          
          <TabsContent value="devices" className="h-[300px] mt-0">
            <div className="h-full w-full flex items-center justify-center text-muted-foreground">
              Device Distribution Chart (Pie Chart visualization would render here)
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Card>
  );
}

// Recent Activity Component
function RecentActivity() {
  const activities = [
    { id: 1, user: "John Doe", action: "Created a new account", time: "2 minutes ago" },
    { id: 2, user: "Jane Smith", action: "Purchased Premium Plan", time: "1 hour ago" },
    { id: 3, user: "Robert Johnson", action: "Updated profile", time: "3 hours ago" },
    { id: 4, user: "Emily Davis", action: "Submitted a support ticket", time: "5 hours ago" },
  ];

  return (
    <Card className="col-span-full lg:col-span-1">
      <div className="p-6">
        <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3 pb-3 border-b border-border last:border-0 last:pb-0">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                {activity.user.charAt(0)}
              </div>
              <div>
                <p className="text-sm font-medium">{activity.user}</p>
                <p className="text-xs text-muted-foreground">{activity.action}</p>
                <p className="text-xs text-muted-foreground mt-1">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
}

// Main Dashboard Component
export function AnalyticsDashboard({ className }: AnalyticsDashboardProps) {
  return (
    <div className={cn("p-6 space-y-6 bg-background", className)}>
      <div className="flex flex-col gap-2">
        <h2 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h2>
        <p className="text-muted-foreground">
          Track your key metrics and performance indicators.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {statsData.map((item) => (
          <StatsCard key={item.name} item={item} />
        ))}
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <ChartSection />
        <RecentActivity />
      </div>
    </div>
  );
}

// Usage Example
export default function AnalyticsDashboardDemo() {
  return (
    <div className="min-h-screen bg-background">
      <AnalyticsDashboard />
    </div>
  );
} 