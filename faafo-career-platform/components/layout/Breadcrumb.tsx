'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: React.ReactNode;
}

interface BreadcrumbProps {
  customItems?: BreadcrumbItem[];
  showHome?: boolean;
  className?: string;
}

// Route mapping for better breadcrumb labels
const routeLabels: Record<string, string> = {
  '/': 'Home',
  '/dashboard': 'Dashboard',
  '/assessment': 'Career Assessment',
  '/career-paths': 'Career Paths',
  '/resources': 'Learning Resources',
  '/forum': 'Community Forum',
  '/freedom-fund': 'Freedom Fund Calculator',
  '/progress': 'Progress Tracker',
  '/recommendations': 'Recommendations',
  '/profile': 'Profile',
  '/help': 'Help & Support',
  '/faq': 'FAQ',
  '/contact': 'Contact',
  '/privacy-policy': 'Privacy Policy',
  '/terms-of-service': 'Terms of Service',
  '/signup': 'Sign Up',
  '/login': 'Log In',
};

// Special route patterns that need custom handling
const specialRoutes: Record<string, (segments: string[]) => BreadcrumbItem[]> = {
  '/career-paths': (segments) => {
    const items: BreadcrumbItem[] = [
      { label: 'Career Paths', href: '/career-paths' }
    ];
    
    if (segments.length > 2) {
      // Individual career path page
      const pathId = segments[2];
      items.push({
        label: 'Path Details',
        href: `/career-paths/${pathId}`
      });
    }
    
    return items;
  },
  
  '/resources': (segments) => {
    const items: BreadcrumbItem[] = [
      { label: 'Learning Resources', href: '/resources' }
    ];
    
    if (segments.length > 2) {
      if (segments[2] === 'categories') {
        items.push({
          label: 'Categories',
          href: '/resources/categories'
        });
        
        if (segments.length > 3) {
          items.push({
            label: segments[3].charAt(0).toUpperCase() + segments[3].slice(1),
            href: `/resources/categories/${segments[3]}`
          });
        }
      } else {
        // Individual resource page
        items.push({
          label: 'Resource Details',
          href: `/resources/${segments[2]}`
        });
      }
    }
    
    return items;
  },
  
  '/forum': (segments) => {
    const items: BreadcrumbItem[] = [
      { label: 'Community Forum', href: '/forum' }
    ];
    
    if (segments.length > 2) {
      if (segments[2] === 'new') {
        items.push({
          label: 'New Post',
          href: '/forum/new'
        });
      } else if (segments[2] === 'posts') {
        items.push({
          label: 'Posts',
          href: '/forum/posts'
        });
        
        if (segments.length > 3) {
          items.push({
            label: 'Post Details',
            href: `/forum/posts/${segments[3]}`
          });
        }
      }
    }
    
    return items;
  }
};

export function Breadcrumb({ customItems, showHome = true, className = '' }: BreadcrumbProps) {
  const pathname = usePathname();

  // If custom items are provided, use them
  if (customItems) {
    return (
      <nav className={`flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400 ${className}`} aria-label="Breadcrumb">
        <ol className="flex items-center space-x-1">
          {showHome && (
            <>
              <li>
                <Link 
                  href="/" 
                  className="flex items-center hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                >
                  <Home className="h-4 w-4" />
                  <span className="sr-only">Home</span>
                </Link>
              </li>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </>
          )}
          
          {customItems.map((item, index) => (
            <React.Fragment key={item.href}>
              <li>
                {index === customItems.length - 1 ? (
                  <span className="flex items-center text-gray-900 dark:text-gray-100 font-medium">
                    {item.icon}
                    {item.label}
                  </span>
                ) : (
                  <Link 
                    href={item.href}
                    className="flex items-center hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                  >
                    {item.icon}
                    {item.label}
                  </Link>
                )}
              </li>
              {index < customItems.length - 1 && (
                <ChevronRight className="h-4 w-4 text-gray-400" />
              )}
            </React.Fragment>
          ))}
        </ol>
      </nav>
    );
  }

  // Auto-generate breadcrumbs from pathname
  const segments = pathname.split('/').filter(Boolean);
  
  // Don't show breadcrumbs on home page
  if (segments.length === 0) {
    return null;
  }

  // Check for special route handling
  const baseRoute = `/${segments[0]}`;
  let breadcrumbItems: BreadcrumbItem[] = [];

  if (specialRoutes[baseRoute]) {
    breadcrumbItems = specialRoutes[baseRoute](segments);
  } else {
    // Generate standard breadcrumbs
    breadcrumbItems = segments.map((segment, index) => {
      const href = '/' + segments.slice(0, index + 1).join('/');
      const label = routeLabels[href] || segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');
      
      return {
        label,
        href
      };
    });
  }

  return (
    <nav className={`flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400 ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {showHome && (
          <>
            <li>
              <Link 
                href="/" 
                className="flex items-center hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
              >
                <Home className="h-4 w-4" />
                <span className="sr-only">Home</span>
              </Link>
            </li>
            <ChevronRight className="h-4 w-4 text-gray-400" />
          </>
        )}
        
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={item.href}>
            <li>
              {index === breadcrumbItems.length - 1 ? (
                <span className="flex items-center text-gray-900 dark:text-gray-100 font-medium">
                  {item.icon}
                  {item.label}
                </span>
              ) : (
                <Link 
                  href={item.href}
                  className="flex items-center hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                >
                  {item.icon}
                  {item.label}
                </Link>
              )}
            </li>
            {index < breadcrumbItems.length - 1 && (
              <ChevronRight className="h-4 w-4 text-gray-400" />
            )}
          </React.Fragment>
        ))}
      </ol>
    </nav>
  );
}

export default Breadcrumb;
