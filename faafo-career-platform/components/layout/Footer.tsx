'use client';

import React from 'react';
import Link from 'next/link';
import { Compass, Mail, ExternalLink } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-background dark:bg-card text-gray-600 dark:text-gray-300 border-t border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              <Compass className="h-8 w-8 mr-2 text-indigo-600 dark:text-indigo-400" />
              <span className="text-2xl font-bold text-foreground">FAAFO</span>
            </div>
            <p className="text-muted-foreground mb-4 max-w-md">
              Empowering career transitions through personalized assessments, financial planning, 
              and community support. Find your path to a fulfilling career.
            </p>
            <div className="flex items-center text-muted-foreground">
              <Mail className="h-4 w-4 mr-2" />
              <a
                href="mailto:<EMAIL>"
                className="hover:text-foreground transition-colors"
              >
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-foreground font-semibold mb-4">Platform</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/assessment" className="hover:text-foreground transition-colors">
                  Career Assessment
                </Link>
              </li>
              <li>
                <Link href="/career-paths" className="hover:text-foreground transition-colors">
                  Career Paths
                </Link>
              </li>
              <li>
                <Link href="/freedom-fund" className="hover:text-foreground transition-colors">
                  Freedom Fund
                </Link>
              </li>
              <li>
                <Link href="/resources" className="hover:text-foreground transition-colors">
                  Resources
                </Link>
              </li>
              <li>
                <Link href="/forum" className="hover:text-foreground transition-colors">
                  Community
                </Link>
              </li>
            </ul>
          </div>

          {/* Support & Legal */}
          <div>
            <h3 className="text-foreground font-semibold mb-4">Support</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="hover:text-foreground transition-colors">
                  Help Center
                </Link>
              </li>
              <li>
                <Link href="/faq" className="hover:text-foreground transition-colors">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-foreground transition-colors">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/privacy-policy" className="hover:text-foreground transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms-of-service" className="hover:text-foreground transition-colors">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-border mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-muted-foreground text-sm">
              © {currentYear} FAAFO Career Platform. All rights reserved.
            </div>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-muted-foreground text-sm">Made with ❤️ for career changers</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
