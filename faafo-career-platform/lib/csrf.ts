import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth';

// CSRF token store (use Redis in production)
const csrfTokens = new Map<string, { token: string; expires: number }>();

export function generateCSRFToken(): string {
  return crypto.randomUUID();
}

export async function getCSRFToken(request: NextRequest): Promise<string> {
  const session = await getServerSession(authOptions);
  const sessionId = session?.user?.id || 'anonymous';
  
  // Check if we have a valid token
  const existing = csrfTokens.get(sessionId);
  if (existing && existing.expires > Date.now()) {
    return existing.token;
  }
  
  // Generate new token
  const token = generateCSRFToken();
  const expires = Date.now() + (60 * 60 * 1000); // 1 hour
  
  csrfTokens.set(sessionId, { token, expires });
  
  // Clean up expired tokens
  Array.from(csrfTokens.entries()).forEach(([key, value]) => {
    if (value.expires <= Date.now()) {
      csrfTokens.delete(key);
    }
  });
  
  return token;
}

export async function validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {
  const session = await getServerSession(authOptions);
  const sessionId = session?.user?.id || 'anonymous';
  
  const stored = csrfTokens.get(sessionId);
  if (!stored || stored.expires <= Date.now()) {
    return false;
  }
  
  return stored.token === token;
}

export async function withCSRFProtection(
  request: NextRequest,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  // Skip CSRF for GET requests
  if (request.method === 'GET') {
    return handler();
  }
  
  // Get CSRF token from header or body
  const csrfToken = request.headers.get('x-csrf-token') || 
                   request.headers.get('csrf-token');
  
  if (!csrfToken) {
    return NextResponse.json(
      { error: 'CSRF token missing' },
      { status: 403 }
    );
  }
  
  const isValid = await validateCSRFToken(request, csrfToken);
  if (!isValid) {
    return NextResponse.json(
      { error: 'Invalid CSRF token' },
      { status: 403 }
    );
  }
  
  return handler();
}

// API endpoint to get CSRF token
export async function getCSRFTokenEndpoint(request: NextRequest): Promise<NextResponse> {
  const token = await getCSRFToken(request);
  return NextResponse.json({ csrfToken: token });
}
