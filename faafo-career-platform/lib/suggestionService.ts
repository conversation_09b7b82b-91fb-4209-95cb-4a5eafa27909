import prisma from './prisma';
// Removed AssessmentResponse, SuggestionRule, Prisma as they are implicitly typed by Prisma Client
import { CareerPath } from '@prisma/client';
import { generateAssessmentInsights, AssessmentResponse } from './assessmentScoring';

// Define a type for the structure of a suggestion result
export interface CareerPathSuggestion {
  careerPath: CareerPath;
  score: number;
  matchReason?: string;
  skillAlignment?: number;
  // We can add matched rules or other metadata later if needed
}

// Define a type for answerValue, allowing for primitives
type AnswerValue = string | number | boolean | null;

/**
 * Generates career path suggestions for a given assessment.
 * 
 * @param assessmentId The ID of the assessment to generate suggestions for.
 * @returns A promise that resolves to an array of CareerPathSuggestion objects, ranked by score.
 */
export async function getCareerPathSuggestions(
  assessmentId: string
): Promise<CareerPathSuggestion[]> {
  try {
    // 1. Fetch assessment responses
    const responses = await prisma.assessmentResponse.findMany({
      where: { assessmentId },
    });
    console.log(`[suggestionService] Fetched responses for assessment ${assessmentId}:`, JSON.stringify(responses, null, 2));

    if (!responses || responses.length === 0) {
      console.log(`No responses found for assessment ID: ${assessmentId}`);
      return [];
    }

    // 2. Fetch all suggestion rules (and their related career paths)
    const rules = await prisma.suggestionRule.findMany({
      include: {
        careerPath: true, // Include the CareerPath data with each rule
      },
    });
    console.log('[suggestionService] Fetched rules:', JSON.stringify(rules, null, 2));

    if (!rules || rules.length === 0) {
      console.log('No suggestion rules found in the database.');
      return [];
    }

    // 3. Calculate scores for each career path
    const careerPathScores: Map<string, { path: CareerPath; score: number }> = new Map();

    for (const rule of rules) {
      console.log(`[suggestionService] Processing rule ID ${rule.id} for path ${rule.careerPath.name} (questionKey: ${rule.questionKey}, expectedAnswer: ${JSON.stringify(rule.answerValue)})`);
      if (!rule.careerPath.isActive) { // Skip rules for inactive career paths
        console.log(`[suggestionService] Skipping rule for inactive career path: ${rule.careerPath.name}`);
        continue;
      }

      const matchingResponse = responses.find(
        (response) => response.questionKey === rule.questionKey
      );
      console.log(`[suggestionService] Matching response for ${rule.questionKey}:`, JSON.stringify(matchingResponse, null, 2));

      if (matchingResponse) {
        let isMatch = false;
        const ruleAnswer = rule.answerValue as AnswerValue; // Use the new type
        const responseAnswer = matchingResponse.answerValue as AnswerValue;
        console.log(`[suggestionService] Rule answer (expected): ${JSON.stringify(ruleAnswer)}, Response answer (actual): ${JSON.stringify(responseAnswer)}`);

        // Basic matching logic (can be expanded significantly)
        if (Array.isArray(responseAnswer) && Array.isArray(ruleAnswer)) {
          // If both are arrays, check if any element in responseAnswer is in ruleAnswer
          // (This is a simple interpretation, could be stricter or more complex)
          isMatch = responseAnswer.some(val => ruleAnswer.includes(val));
        } else if (Array.isArray(responseAnswer)) {
          // If response is array and rule is single value, check if rule value is in response array
          isMatch = responseAnswer.includes(ruleAnswer);
        } else if (Array.isArray(ruleAnswer)) {
          // If rule is array and response is single value, check if response value is in rule array
          isMatch = ruleAnswer.includes(responseAnswer);
        } else {
          // Direct equality for non-array types
          isMatch = responseAnswer === ruleAnswer;
        }

        if (isMatch) {
          console.log(`[suggestionService] MATCH FOUND for rule ID ${rule.id} on question ${rule.questionKey}!`);
          const current = careerPathScores.get(rule.careerPathId) || {
            path: rule.careerPath,
            score: 0,
          };
          current.score += rule.weight;
          careerPathScores.set(rule.careerPathId, current);
        } else {
          console.log(`[suggestionService] NO MATCH for rule ID ${rule.id} on question ${rule.questionKey}.`);
        }
      }
    }

    console.log('[suggestionService] Calculated careerPathScores:', JSON.stringify(Array.from(careerPathScores.entries()), null, 2));

    // 4. Generate assessment insights for enhanced matching
    const responseData: AssessmentResponse = {};
    responses.forEach(response => {
      responseData[response.questionKey] = response.answerValue as any;
    });
    const insights = generateAssessmentInsights(responseData);

    // 5. Convert map to array, sort by score, and format with enhanced data
    const suggestions: CareerPathSuggestion[] = Array.from(careerPathScores.values())
      .map(item => ({
        careerPath: item.path,
        score: item.score,
        matchReason: generateMatchReason(item.path, insights, item.score),
        skillAlignment: calculateSkillAlignment(item.path, insights.topSkills),
      }))
      .sort((a, b) => b.score - a.score); // Sort descending by score

    console.log('[suggestionService] Final suggestions:', JSON.stringify(suggestions, null, 2));
    return suggestions;

  } catch (error) {
    console.error('Error generating career path suggestions:', error);
    // It might be better to throw the error or return a specific error structure
    // For now, returning an empty array on error to prevent crashes in consuming code
    return [];
  }
}

/**
 * Generate a human-readable match reason for a career path suggestion
 */
function generateMatchReason(careerPath: CareerPath, insights: any, score: number): string {
  const reasons = [];

  // Check skill alignment
  const skillMatches = insights.topSkills.filter((skill: string) =>
    careerPath.name.toLowerCase().includes(skill.toLowerCase()) ||
    careerPath.overview.toLowerCase().includes(skill.toLowerCase())
  );

  if (skillMatches.length > 0) {
    reasons.push(`Matches ${skillMatches.length} of your key skills`);
  }

  // Check motivation alignment
  if (insights.primaryMotivation &&
      careerPath.overview.toLowerCase().includes(insights.primaryMotivation.toLowerCase())) {
    reasons.push('Aligns with your career motivation');
  }

  // Score-based reasons
  if (score >= 8) {
    reasons.push('Excellent compatibility');
  } else if (score >= 5) {
    reasons.push('Good compatibility');
  } else if (score >= 3) {
    reasons.push('Moderate compatibility');
  }

  // Readiness-based reasons
  if (insights.scores.readinessScore >= 80) {
    reasons.push('Well-suited for immediate transition');
  } else if (insights.scores.readinessScore >= 60) {
    reasons.push('Good preparation for transition');
  }

  return reasons.length > 0 ? reasons.join(', ') : 'Based on your assessment responses';
}

/**
 * Calculate skill alignment percentage between career path and user skills
 */
function calculateSkillAlignment(careerPath: CareerPath, userSkills: string[]): number {
  if (!userSkills.length) return 0;

  const careerText = `${careerPath.name} ${careerPath.overview}`.toLowerCase();
  const matchingSkills = userSkills.filter(skill =>
    careerText.includes(skill.toLowerCase())
  );

  return Math.round((matchingSkills.length / userSkills.length) * 100);
}

// Example of how you might seed some data (for testing purposes)
// This would typically be in a separate seed script
/*
async function seedExampleData() {
  // Create a sample career path
  const freelancePath = await prisma.careerPath.upsert({
    where: { slug: 'freelancing' },
    update: {},
    create: {
      name: 'Freelancing',
      slug: 'freelancing',
      overview: 'Become your own boss and offer your skills to clients on a project basis.',
      pros: ['Flexibility', 'Autonomy', 'Diverse projects'],
      cons: ['Income instability', 'Admin overhead', 'Isolation'],
      actionableSteps: Prisma.JsonNull, // Or provide actual JSON: { steps: [{...}] }
    },
  });

  // Create a sample rule for this path
  await prisma.suggestionRule.upsert({
    where: {
      // Need a unique constraint for rules or a specific ID to upsert reliably
      // For now, we assume creation without checking for duplicates beyond this example
      // A composite key on (careerPathId, questionKey, answerValueHash) might be good.
      // This is a placeholder upsert and might create duplicates without a proper unique constraint.
      // careerPathId_questionKey: { careerPathId: freelancePath.id, questionKey: 'desired_outcomes_work_life' }
      // Commenting out the where clause that is causing a type error due to non-existent composite key
    },
    update: {},
    create: {
      careerPathId: freelancePath.id,
      questionKey: 'desired_outcomes_work_life',
      answerValue: 'critical', // If user answers 'critical' for work-life balance
      weight: 2.0,
      notes: 'Strong indicator for freelancing.',
    },
  });

  console.log('Seeded example career path and suggestion rule.');
}

// seedExampleData().catch(e => console.error(e));
*/ 