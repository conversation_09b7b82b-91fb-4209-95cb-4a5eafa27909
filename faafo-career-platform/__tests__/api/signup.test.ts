import { NextRequest } from 'next/server';
import { POST } from '@/app/api/signup/route';
import prisma from '@/lib/prisma';
import { sendEmail } from '@/lib/email';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  user: {
    findUnique: jest.fn(),
    create: jest.fn(),
  },
  verificationToken: {
    create: jest.fn(),
  },
}));

jest.mock('@/lib/email', () => ({
  sendEmail: jest.fn(),
}));

jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
}));

jest.mock('uuid', () => ({
  v4: jest.fn(),
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockSendEmail = sendEmail as jest.MockedFunction<typeof sendEmail>;
const mockBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;
const mockUuidv4 = uuidv4 as jest.MockedFunction<typeof uuidv4>;

describe('/api/signup', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.NEXTAUTH_URL = 'http://localhost:3000';
  });

  it('should create user and send verification email successfully', async () => {
    const email = '<EMAIL>';
    const password = 'password123';
    const hashedPassword = 'hashed_password_123';
    const token = 'mock-uuid-token';
    const userId = 'user123';

    // Mock dependencies
    mockPrisma.user.findUnique.mockResolvedValue(null); // User doesn't exist
    mockBcrypt.hash.mockResolvedValue(hashedPassword);
    mockUuidv4.mockReturnValue(token);

    const newUser = {
      id: userId,
      email: email,
      password: hashedPassword,
      emailVerified: null,
      name: null,
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    };

    mockPrisma.user.create.mockResolvedValue(newUser);
    mockPrisma.verificationToken.create.mockResolvedValue({
      identifier: email,
      token: token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
    });
    mockSendEmail.mockResolvedValue({ success: true, data: { id: 'email123' } });

    const request = new NextRequest('http://localhost:3000/api/signup', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(201);
    expect(data.message).toBe('User created successfully. Please check your email to verify your account.');
    expect(data.requiresVerification).toBe(true);

    // Verify user creation
    expect(mockPrisma.user.create).toHaveBeenCalledWith({
      data: {
        email,
        password: hashedPassword,
        emailVerified: null,
      },
    });

    // Verify verification token creation
    expect(mockPrisma.verificationToken.create).toHaveBeenCalledWith({
      data: {
        identifier: email,
        token: token,
        expires: expect.any(Date),
      },
    });

    // Verify email sending
    expect(mockSendEmail).toHaveBeenCalledWith({
      to: email,
      subject: 'Verify your email for FAAFO Career Platform',
      template: expect.any(Object),
    });
  });

  it('should return error for existing user', async () => {
    const email = '<EMAIL>';
    const password = 'password123';

    // Mock existing user
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'existing123',
      email: email,
      password: 'existing_password',
      emailVerified: new Date(),
      name: 'Existing User',
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    });

    const request = new NextRequest('http://localhost:3000/api/signup', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(409);
    expect(data.message).toBe('User already exists');
    expect(mockPrisma.user.create).not.toHaveBeenCalled();
    expect(mockSendEmail).not.toHaveBeenCalled();
  });

  it('should create user even if email sending fails', async () => {
    const email = '<EMAIL>';
    const password = 'password123';
    const hashedPassword = 'hashed_password_123';
    const token = 'mock-uuid-token';
    const userId = 'user123';

    mockPrisma.user.findUnique.mockResolvedValue(null);
    mockBcrypt.hash.mockResolvedValue(hashedPassword);
    mockUuidv4.mockReturnValue(token);

    const newUser = {
      id: userId,
      email: email,
      password: hashedPassword,
      emailVerified: null,
      name: null,
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    };

    mockPrisma.user.create.mockResolvedValue(newUser);
    mockPrisma.verificationToken.create.mockResolvedValue({
      identifier: email,
      token: token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
    });

    // Mock email sending failure
    mockSendEmail.mockRejectedValue(new Error('Email service unavailable'));

    const request = new NextRequest('http://localhost:3000/api/signup', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    // Should still succeed even if email fails
    expect(response.status).toBe(201);
    expect(data.message).toBe('User created successfully. Please check your email to verify your account.');
    expect(data.requiresVerification).toBe(true);

    // User should still be created
    expect(mockPrisma.user.create).toHaveBeenCalled();
    expect(mockPrisma.verificationToken.create).toHaveBeenCalled();
  });

  it('should return error for missing email or password', async () => {
    const request = new NextRequest('http://localhost:3000/api/signup', {
      method: 'POST',
      body: JSON.stringify({ email: '', password: '' }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.message).toBe('Something went wrong');
  });

  it('should handle database errors gracefully', async () => {
    const email = '<EMAIL>';
    const password = 'password123';

    mockPrisma.user.findUnique.mockResolvedValue(null);
    mockBcrypt.hash.mockResolvedValue('hashed_password');
    
    // Mock database error
    mockPrisma.user.create.mockRejectedValue(new Error('Database connection failed'));

    const request = new NextRequest('http://localhost:3000/api/signup', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.message).toBe('Something went wrong');
  });

  it('should generate proper verification URL', async () => {
    const email = '<EMAIL>';
    const password = 'password123';
    const token = 'mock-uuid-token';

    mockPrisma.user.findUnique.mockResolvedValue(null);
    mockBcrypt.hash.mockResolvedValue('hashed_password');
    mockUuidv4.mockReturnValue(token);

    mockPrisma.user.create.mockResolvedValue({
      id: 'user123',
      email: email,
      password: 'hashed_password',
      emailVerified: null,
      name: null,
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    });

    mockPrisma.verificationToken.create.mockResolvedValue({
      identifier: email,
      token: token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
    });

    mockSendEmail.mockResolvedValue({ success: true, data: { id: 'email123' } });

    const request = new NextRequest('http://localhost:3000/api/signup', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
      headers: { 'Content-Type': 'application/json' },
    });

    await POST(request);

    // Verify the email was called with correct verification URL
    expect(mockSendEmail).toHaveBeenCalledWith({
      to: email,
      subject: 'Verify your email for FAAFO Career Platform',
      template: expect.any(Object),
    });

    // The verification URL should be properly formatted
    const expectedUrl = `http://localhost:3000/auth/verify-email?token=${token}&email=${encodeURIComponent(email)}`;
    // We can't directly test the URL since it's passed to React.createElement,
    // but we can verify the email function was called
  });

  it('should set proper token expiry (24 hours)', async () => {
    const email = '<EMAIL>';
    const password = 'password123';
    const token = 'mock-uuid-token';

    mockPrisma.user.findUnique.mockResolvedValue(null);
    mockBcrypt.hash.mockResolvedValue('hashed_password');
    mockUuidv4.mockReturnValue(token);

    mockPrisma.user.create.mockResolvedValue({
      id: 'user123',
      email: email,
      password: 'hashed_password',
      emailVerified: null,
      name: null,
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    });

    mockPrisma.verificationToken.create.mockResolvedValue({
      identifier: email,
      token: token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
    });

    mockSendEmail.mockResolvedValue({ success: true, data: { id: 'email123' } });

    const request = new NextRequest('http://localhost:3000/api/signup', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
      headers: { 'Content-Type': 'application/json' },
    });

    await POST(request);

    // Verify token expiry is set to 24 hours from now
    const createCall = mockPrisma.verificationToken.create.mock.calls[0][0];
    const expiryTime = createCall.data.expires.getTime();
    const now = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000;

    // Allow for small time differences in test execution
    expect(expiryTime).toBeGreaterThan(now + twentyFourHours - 1000);
    expect(expiryTime).toBeLessThan(now + twentyFourHours + 1000);
  });
});
