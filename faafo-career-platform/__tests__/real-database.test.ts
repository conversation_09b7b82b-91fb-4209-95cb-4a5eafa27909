/**
 * Real Database Integration Tests
 *
 * These tests use actual database connections instead of mocks
 * to verify that our application works with real data.
 */

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

// Load environment variables
require('dotenv').config();

// Create a real Prisma client directly, bypassing any mocks
const realPrisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: ['error'],
});

// Simple test setup without the TestSetup class
async function setupRealDatabase() {
  console.log('Setting up real test database...');
  try {
    await realPrisma.$connect();
    console.log('✅ Real database connected successfully');
    return realPrisma;
  } catch (error) {
    console.error('❌ Real database setup failed:', error);
    throw error;
  }
}

async function cleanupRealTestData() {
  console.log('Cleaning up real test data...');
  try {
    // Clean up test data only (not all data)
    await realPrisma.forumReply.deleteMany({
      where: {
        author: {
          email: {
            contains: 'test',
          },
        },
      },
    });

    await realPrisma.forumPost.deleteMany({
      where: {
        author: {
          email: {
            contains: 'test',
          },
        },
      },
    });

    await realPrisma.assessment.deleteMany({
      where: {
        user: {
          email: {
            contains: 'test',
          },
        },
      },
    });

    await realPrisma.learningResource.deleteMany({
      where: {
        title: {
          contains: 'Test',
        },
      },
    });

    await realPrisma.user.deleteMany({
      where: {
        email: {
          contains: 'test',
        },
      },
    });

    console.log('✅ Real test data cleaned up successfully');
  } catch (error) {
    console.error('❌ Real test data cleanup failed:', error);
    throw error;
  }
}

async function seedRealTestData() {
  console.log('Seeding real test data...');

  const hashedPassword = await bcrypt.hash('testpassword123', 10);

  try {
    const testUser1 = await realPrisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User 1',
        emailVerified: new Date(),
      },
    });

    console.log('Created real testUser1:', testUser1);

    if (!testUser1 || !testUser1.id) {
      throw new Error(`testUser1 creation failed - got: ${JSON.stringify(testUser1)}`);
    }

    const testUser2 = await realPrisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test User 2',
        emailVerified: new Date(),
      },
    });

    await realPrisma.learningResource.upsert({
    where: { id: 'test-resource-1' },
    update: {},
    create: {
      id: 'test-resource-1',
      title: 'Test Learning Resource 1',
      description: 'A test learning resource for testing purposes',
      url: 'https://example.com/resource1',
      type: 'ARTICLE',
      category: 'WEB_DEVELOPMENT',
      skillLevel: 'BEGINNER',
      format: 'SELF_PACED',
      duration: '30 minutes',
    },
  });

  await realPrisma.assessment.upsert({
    where: { id: 'test-assessment-1' },
    update: {},
    create: {
      id: 'test-assessment-1',
      userId: testUser1.id,
      status: 'COMPLETED',
      currentStep: 5,
      completedAt: new Date(),
      responses: {
        create: [
          {
            questionKey: 'dissatisfaction_triggers',
            answerValue: ['lack_of_growth'],
          },
          {
            questionKey: 'desired_outcomes_skill_a',
            answerValue: 'high',
          },
        ],
      },
    },
  });

    console.log('✅ Real test data seeded successfully');
  } catch (error) {
    console.error('❌ Real test data seeding failed:', error);
    throw error;
  }
}

describe('Real Database Integration Tests', () => {
  let prisma: PrismaClient;

  beforeAll(async () => {
    prisma = await setupRealDatabase();
  });

  afterAll(async () => {
    await realPrisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up test data before each test for isolation
    await cleanupRealTestData();
    await seedRealTestData();
  });

  describe('User Operations', () => {
    it('should create a new user with real database', async () => {
      const hashedPassword = await bcrypt.hash('newpassword123', 10);
      const uniqueEmail = `newuser-${Date.now()}@example.com`;

      const newUser = await realPrisma.user.create({
        data: {
          email: uniqueEmail,
          password: hashedPassword,
          name: 'New Test User',
        },
      });

      expect(newUser).toBeDefined();
      expect(newUser.email).toBe(uniqueEmail);
      expect(newUser.name).toBe('New Test User');
      expect(newUser.id).toBeDefined();
      expect(newUser.createdAt).toBeInstanceOf(Date);
    });

    it('should find existing users', async () => {
      const users = await realPrisma.user.findMany();

      expect(users.length).toBeGreaterThan(0);
      expect(users[0]).toHaveProperty('email');
      expect(users[0]).toHaveProperty('name');
      expect(users[0]).toHaveProperty('id');
    });

    it('should update user information', async () => {
      const user = await realPrisma.user.findFirst({
        where: { email: '<EMAIL>' },
      });

      expect(user).toBeDefined();

      const updatedUser = await realPrisma.user.update({
        where: { id: user!.id },
        data: { name: 'Updated Test User' },
      });

      expect(updatedUser.name).toBe('Updated Test User');
      expect(updatedUser.email).toBe('<EMAIL>');
    });

    it('should handle unique email constraint', async () => {
      const hashedPassword = await bcrypt.hash('password123', 10);

      // Try to create user with existing email
      await expect(
        realPrisma.user.create({
          data: {
            email: '<EMAIL>', // This email already exists
            password: hashedPassword,
            name: 'Duplicate User',
          },
        })
      ).rejects.toThrow();
    });
  });

  describe('Learning Resource Operations', () => {
    it('should create learning resources', async () => {
      const uniqueUrl = `https://example.com/new-resource-${Date.now()}`;

      const resource = await realPrisma.learningResource.create({
        data: {
          title: 'New Learning Resource',
          description: 'A brand new learning resource',
          url: uniqueUrl,
          type: 'VIDEO',
          category: 'WEB_DEVELOPMENT',
          skillLevel: 'INTERMEDIATE',
          format: 'SELF_PACED',
          duration: '60 minutes',
        },
      });

      expect(resource).toBeDefined();
      expect(resource.title).toBe('New Learning Resource');
      expect(resource.skillLevel).toBe('INTERMEDIATE');
      expect(resource.duration).toBe('60 minutes');
    });

    it('should find learning resources by category', async () => {
      const techResources = await realPrisma.learningResource.findMany({
        where: { category: 'WEB_DEVELOPMENT' },
      });

      expect(techResources.length).toBeGreaterThan(0);
      techResources.forEach(resource => {
        expect(resource.category).toBe('WEB_DEVELOPMENT');
      });
    });

    it('should filter resources by skill level', async () => {
      const beginnerResources = await realPrisma.learningResource.findMany({
        where: { skillLevel: 'BEGINNER' },
      });

      beginnerResources.forEach(resource => {
        expect(resource.skillLevel).toBe('BEGINNER');
      });
    });
  });

  describe('Assessment Operations', () => {
    it('should create assessments linked to users', async () => {
      const user = await realPrisma.user.findFirst({
        where: { email: '<EMAIL>' },
      });

      expect(user).toBeDefined();

      const assessment = await realPrisma.assessment.create({
        data: {
          userId: user!.id,
          status: 'COMPLETED',
          currentStep: 5,
          completedAt: new Date(),
          responses: {
            create: [
              {
                questionKey: 'dissatisfaction_triggers',
                answerValue: ['lack_of_growth'],
              },
              {
                questionKey: 'desired_outcomes_skill_a',
                answerValue: 'high',
              },
            ],
          },
        },
      });

      expect(assessment).toBeDefined();
      expect(assessment.userId).toBe(user!.id);
      expect(assessment.status).toBe('COMPLETED');
      expect(assessment.currentStep).toBe(5);
    });

    it('should find assessments by user', async () => {
      const user = await realPrisma.user.findFirst({
        where: { email: '<EMAIL>' },
      });

      const assessments = await realPrisma.assessment.findMany({
        where: { userId: user!.id },
        include: { user: true },
      });

      expect(assessments.length).toBeGreaterThan(0);
      assessments.forEach(assessment => {
        expect(assessment.userId).toBe(user!.id);
        expect(assessment.user.email).toBe('<EMAIL>');
      });
    });
  });

  describe('Forum Operations', () => {
    it('should create forum posts with replies', async () => {
      const user = await realPrisma.user.findFirst({
        where: { email: '<EMAIL>' },
      });

      const post = await realPrisma.forumPost.create({
        data: {
          title: 'Test Forum Post',
          content: 'This is a test forum post',
          authorId: user!.id,
          tags: ['test', 'forum'],
        },
      });

      const reply = await realPrisma.forumReply.create({
        data: {
          content: 'This is a test reply',
          authorId: user!.id,
          postId: post.id,
        },
      });

      expect(post).toBeDefined();
      expect(reply).toBeDefined();
      expect(reply.postId).toBe(post.id);
    });

    it('should find posts with their replies', async () => {
      // First create a post and reply to ensure we have data
      const user = await realPrisma.user.findFirst({
        where: { email: '<EMAIL>' },
      });

      const testPost = await realPrisma.forumPost.create({
        data: {
          title: 'Test Post for Replies',
          content: 'This post should have replies',
          authorId: user!.id,
          tags: ['test', 'replies'],
        },
      });

      await realPrisma.forumReply.create({
        data: {
          content: 'Test reply to the post',
          authorId: user!.id,
          postId: testPost.id,
        },
      });

      const postsWithReplies = await realPrisma.forumPost.findMany({
        include: {
          replies: true,
          author: true,
        },
      });

      expect(postsWithReplies.length).toBeGreaterThan(0);
      postsWithReplies.forEach(post => {
        expect(post.author).toBeDefined();
        expect(post.author.email).toBeDefined();
      });
    });
  });

  describe('Database Performance', () => {
    it('should handle concurrent operations', async () => {
      const timestamp = Date.now();
      const promises = Array.from({ length: 10 }, (_, i) =>
        realPrisma.user.create({
          data: {
            email: `concurrent${i}-${timestamp}@example.com`,
            password: 'hashedpassword',
            name: `Concurrent User ${i}`,
          },
        })
      );

      const users = await Promise.all(promises);
      expect(users).toHaveLength(10);

      // Verify all users were created
      const count = await realPrisma.user.count({
        where: {
          email: {
            startsWith: `concurrent`,
            contains: `${timestamp}`,
          },
        },
      });
      expect(count).toBe(10);
    });

    it('should handle transactions', async () => {
      const user = await realPrisma.user.findFirst();

      await realPrisma.$transaction(async (tx) => {
        const post = await tx.forumPost.create({
          data: {
            title: 'Transaction Test Post',
            content: 'Testing transaction',
            authorId: user!.id,
            tags: ['transaction', 'test'],
          },
        });

        await tx.forumReply.create({
          data: {
            content: 'Transaction test reply',
            authorId: user!.id,
            postId: post.id,
          },
        });
      });

      const post = await realPrisma.forumPost.findFirst({
        where: { title: 'Transaction Test Post' },
        include: { replies: true },
      });

      expect(post).toBeDefined();
      expect(post!.replies).toHaveLength(1);
    });
  });
});
