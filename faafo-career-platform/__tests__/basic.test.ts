/**
 * Basic Test Suite
 * Verifies that the testing environment is properly configured
 * Now includes real database connection testing
 */

import { PrismaClient } from '@prisma/client';

describe('Basic Testing Environment', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2);
  });

  it('should support TypeScript', () => {
    const testObject: { name: string; value: number } = {
      name: 'test',
      value: 42
    };

    expect(testObject.name).toBe('test');
    expect(testObject.value).toBe(42);
  });

  it('should support async operations', async () => {
    const asyncFunction = async (): Promise<string> => {
      return new Promise((resolve) => {
        setTimeout(() => resolve('async result'), 10);
      });
    };

    const result = await asyncFunction();
    expect(result).toBe('async result');
  });

  it('should have environment variables', () => {
    expect(process.env.NODE_ENV).toBeDefined();
    expect(process.env.DATABASE_URL).toBeDefined();
  });

  it('should have mock prisma available', () => {
    // Check if global mock is available
    const globalMock = (global as any).mockPrisma;
    expect(globalMock).toBeDefined();
    expect(globalMock.user).toBeDefined();
    expect(globalMock.$connect).toBeDefined();
    console.log('✅ Global mock Prisma is available');
  });

  it('should connect to mock database', async () => {
    // Use the global mock directly
    const mockPrisma = (global as any).mockPrisma;

    expect(mockPrisma).toBeDefined();
    expect(mockPrisma.$connect).toBeDefined();

    // Test mock database connection
    mockPrisma.$connect.mockResolvedValue(undefined);
    await mockPrisma.$connect();

    // Test a simple mock query
    mockPrisma.$queryRaw.mockResolvedValue([{ test: 1 }]);
    const result = await mockPrisma.$queryRaw`SELECT 1 as test`;
    expect(result).toBeDefined();
    expect(result).toEqual([{ test: 1 }]);

    console.log('✅ Mock database connection successful');
  });

  it('should be able to count users in mock database', async () => {
    // Use the global mock directly
    const mockPrisma = (global as any).mockPrisma;

    expect(mockPrisma).toBeDefined();
    expect(mockPrisma.user).toBeDefined();
    expect(mockPrisma.user.count).toBeDefined();

    // Mock user count
    mockPrisma.user.count.mockResolvedValue(5);

    const userCount = await mockPrisma.user.count();
    expect(typeof userCount).toBe('number');
    expect(userCount).toBeGreaterThanOrEqual(0);
    expect(userCount).toBe(5);

    console.log(`✅ Found ${userCount} users in mock database`);
  });
});
