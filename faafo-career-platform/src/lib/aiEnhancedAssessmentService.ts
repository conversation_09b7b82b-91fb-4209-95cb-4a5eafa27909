import { AssessmentResponse, AssessmentInsights } from './assessmentScoring';
import { EnhancedAssessmentResults, CareerPathRecommendation } from './enhancedAssessmentService';
import { geminiService } from './services/geminiService';
import prisma from './prisma';

export interface AIInsights {
  personalityAnalysis: PersonalityAnalysis;
  careerFitAnalysis: CareerFitAnalysis[];
  skillGapInsights: SkillGapInsights;
  learningStyleRecommendations: LearningStyleRecommendations;
  marketTrendAnalysis: MarketTrendAnalysis;
  personalizationScore: number;
  confidenceLevel: number;
  generatedAt: string;
  version: string;
}

export interface PersonalityAnalysis {
  workStyle: string;
  motivationFactors: string[];
  preferredEnvironment: string;
  communicationStyle: string;
  leadershipPotential: string;
  stressManagement: string;
  adaptabilityScore: number;
  teamworkPreference: string;
  innovationTendency: string;
  riskTolerance: string;
}

export interface CareerFitAnalysis {
  careerPath: string;
  fitScore: number;
  aiReasoning: string;
  personalityAlignment: string[];
  potentialChallenges: string[];
  successPredictors: string[];
  marketOutlook: string;
  salaryGrowthPotential: string;
  workLifeBalanceRating: number;
  stressLevel: number;
}

export interface SkillGapInsights {
  criticalGaps: CriticalSkillGap[];
  hiddenStrengths: string[];
  transferableSkills: string[];
  learningPriority: LearningPriorityInsight[];
  timeToCompetency: TimeToCompetencyAnalysis;
  alternativePathways: string[];
}

export interface CriticalSkillGap {
  skill: string;
  importance: number;
  currentLevel: number;
  marketDemand: string;
  learningDifficulty: string;
  aiRecommendation: string;
  prerequisiteSkills: string[];
  complementarySkills: string[];
}

export interface LearningPriorityInsight {
  skill: string;
  priority: number;
  reasoning: string;
  optimalLearningPath: string;
  estimatedHours: number;
  milestones: string[];
}

export interface LearningStyleRecommendations {
  primaryLearningStyle: string;
  recommendedFormats: string[];
  studySchedule: StudyScheduleRecommendation;
  motivationTechniques: string[];
  progressTrackingMethods: string[];
  socialLearningPreference: string;
}

export interface StudyScheduleRecommendation {
  optimalSessionLength: string;
  frequencyPerWeek: number;
  bestTimeOfDay: string;
  breakIntervals: string;
  reviewSchedule: string;
}

export interface MarketTrendAnalysis {
  industryGrowth: string;
  emergingSkills: string[];
  decliningSkills: string[];
  salaryTrends: string;
  remoteWorkOpportunities: string;
  geographicHotspots: string[];
  futureOutlook: string;
}

export interface TimeToCompetencyAnalysis {
  optimisticTimeline: string;
  realisticTimeline: string;
  conservativeTimeline: string;
  factorsAffectingSpeed: string[];
  accelerationOpportunities: string[];
}

export class AIEnhancedAssessmentService {
  private geminiService: typeof geminiService;

  constructor() {
    this.geminiService = geminiService;
  }

  async generateAIInsights(
    assessmentId: string,
    responses: AssessmentResponse,
    insights: AssessmentInsights,
    careerRecommendations: CareerPathRecommendation[],
    userId: string
  ): Promise<AIInsights> {
    try {
      // Generate comprehensive AI analysis
      const [
        personalityAnalysis,
        careerFitAnalysis,
        skillGapInsights,
        learningStyleRecommendations,
        marketTrendAnalysis
      ] = await Promise.all([
        this.generatePersonalityAnalysis(responses, insights, assessmentId, userId),
        this.generateCareerFitAnalysis(responses, careerRecommendations, userId),
        this.generateSkillGapInsights(responses, insights, careerRecommendations, userId),
        this.generateLearningStyleRecommendations(responses, insights, userId),
        this.generateMarketTrendAnalysis(careerRecommendations, userId)
      ]);

      // Calculate personalization and confidence scores
      const personalizationScore = this.calculatePersonalizationScore(responses, insights);
      const confidenceLevel = this.calculateConfidenceLevel(
        personalityAnalysis,
        careerFitAnalysis,
        skillGapInsights
      );

      return {
        personalityAnalysis,
        careerFitAnalysis,
        skillGapInsights,
        learningStyleRecommendations,
        marketTrendAnalysis,
        personalizationScore,
        confidenceLevel,
        generatedAt: new Date().toISOString(),
        version: '1.0.0'
      };
    } catch (error) {
      console.error('Error generating AI insights:', error);
      // Return fallback insights
      return this.generateFallbackInsights();
    }
  }

  private async generatePersonalityAnalysis(
    responses: AssessmentResponse,
    insights: AssessmentInsights,
    assessmentId: string,
    userId: string
  ): Promise<PersonalityAnalysis> {
    const prompt = `
Analyze the following career assessment responses and provide a comprehensive personality analysis for career planning:

Assessment Responses: ${JSON.stringify(responses, null, 2)}
Current Insights: ${JSON.stringify(insights, null, 2)}

Provide a detailed personality analysis in JSON format with the following structure:
{
  "workStyle": "Description of preferred work style",
  "motivationFactors": ["factor1", "factor2", "factor3"],
  "preferredEnvironment": "Description of ideal work environment",
  "communicationStyle": "Communication preferences",
  "leadershipPotential": "Assessment of leadership capabilities",
  "stressManagement": "Stress handling approach",
  "adaptabilityScore": 85,
  "teamworkPreference": "Team vs individual work preference",
  "innovationTendency": "Approach to innovation and change",
  "riskTolerance": "Risk-taking comfort level"
}

Focus on career-relevant personality traits that impact job performance and satisfaction.
`;

    const result = await this.geminiService.generatePersonalizedContent(
      'personality_analysis',
      { responses, insights },
      { assessmentId },
      userId
    );

    if (result.success && result.data) {
      return result.data as PersonalityAnalysis;
    }

    // Fallback personality analysis
    return {
      workStyle: "Collaborative and goal-oriented",
      motivationFactors: ["Growth opportunities", "Meaningful work", "Recognition"],
      preferredEnvironment: "Dynamic and supportive workplace",
      communicationStyle: "Direct and collaborative",
      leadershipPotential: "Strong potential with development",
      stressManagement: "Proactive and solution-focused",
      adaptabilityScore: 75,
      teamworkPreference: "Balanced team and individual work",
      innovationTendency: "Open to new ideas and approaches",
      riskTolerance: "Moderate risk tolerance"
    };
  }

  private async generateCareerFitAnalysis(
    responses: AssessmentResponse,
    careerRecommendations: CareerPathRecommendation[],
    userId: string
  ): Promise<CareerFitAnalysis[]> {
    const prompt = `
Analyze career fit for the following career recommendations based on assessment responses:

Assessment Responses: ${JSON.stringify(responses, null, 2)}
Career Recommendations: ${JSON.stringify(careerRecommendations.slice(0, 3), null, 2)}

For each career path, provide detailed fit analysis in JSON format:
[
  {
    "careerPath": "Career Path Name",
    "fitScore": 85,
    "aiReasoning": "Detailed AI reasoning for the fit score",
    "personalityAlignment": ["trait1", "trait2", "trait3"],
    "potentialChallenges": ["challenge1", "challenge2"],
    "successPredictors": ["predictor1", "predictor2"],
    "marketOutlook": "Market analysis for this career",
    "salaryGrowthPotential": "Salary growth analysis",
    "workLifeBalanceRating": 8,
    "stressLevel": 6
  }
]

Provide honest, data-driven analysis that helps users make informed decisions.
`;

    const result = await this.geminiService.generatePersonalizedContent(
      'career_fit_analysis',
      { responses, careerRecommendations: careerRecommendations.slice(0, 3) },
      {},
      userId
    );

    if (result.success && result.data && Array.isArray(result.data)) {
      return result.data as CareerFitAnalysis[];
    }

    // Fallback analysis
    return careerRecommendations.slice(0, 3).map((career, index) => ({
      careerPath: career.name,
      fitScore: career.matchPercentage,
      aiReasoning: `Strong alignment based on your skills and interests in ${career.name}`,
      personalityAlignment: ["Analytical thinking", "Problem-solving", "Communication"],
      potentialChallenges: ["Skill development needed", "Market competition"],
      successPredictors: ["Strong foundation", "Growth mindset", "Relevant experience"],
      marketOutlook: "Positive growth expected",
      salaryGrowthPotential: "Good advancement opportunities",
      workLifeBalanceRating: 7,
      stressLevel: 5
    }));
  }

  private async generateSkillGapInsights(
    responses: AssessmentResponse,
    insights: AssessmentInsights,
    careerRecommendations: CareerPathRecommendation[],
    userId: string
  ): Promise<SkillGapInsights> {
    const topCareer = careerRecommendations[0];
    if (!topCareer) {
      return this.generateFallbackSkillGapInsights();
    }

    const prompt = `
Analyze skill gaps and provide intelligent insights for career transition:

Current Skills: ${JSON.stringify(insights.topSkills, null, 2)}
Target Career: ${topCareer.name}
Skill Gaps: ${JSON.stringify(topCareer.skillAlignment.skillGaps, null, 2)}
Assessment Data: ${JSON.stringify(responses, null, 2)}

Provide comprehensive skill gap analysis in JSON format:
{
  "criticalGaps": [
    {
      "skill": "Skill name",
      "importance": 9,
      "currentLevel": 2,
      "marketDemand": "High demand analysis",
      "learningDifficulty": "Moderate difficulty",
      "aiRecommendation": "Specific learning recommendation",
      "prerequisiteSkills": ["prereq1", "prereq2"],
      "complementarySkills": ["comp1", "comp2"]
    }
  ],
  "hiddenStrengths": ["strength1", "strength2"],
  "transferableSkills": ["skill1", "skill2"],
  "learningPriority": [
    {
      "skill": "Priority skill",
      "priority": 10,
      "reasoning": "Why this skill is priority",
      "optimalLearningPath": "Recommended learning approach",
      "estimatedHours": 120,
      "milestones": ["milestone1", "milestone2"]
    }
  ],
  "timeToCompetency": {
    "optimisticTimeline": "3-4 months",
    "realisticTimeline": "6-8 months",
    "conservativeTimeline": "10-12 months",
    "factorsAffectingSpeed": ["factor1", "factor2"],
    "accelerationOpportunities": ["opportunity1", "opportunity2"]
  },
  "alternativePathways": ["pathway1", "pathway2"]
}
`;

    const result = await this.geminiService.generatePersonalizedContent(
      'skill_gap_insights',
      { responses, insights, topCareer, skillGaps: topCareer.skillAlignment.skillGaps },
      {},
      userId
    );

    if (result.success && result.data) {
      return result.data as SkillGapInsights;
    }

    return this.generateFallbackSkillGapInsights();
  }

  private async generateLearningStyleRecommendations(
    responses: AssessmentResponse,
    insights: AssessmentInsights,
    userId: string
  ): Promise<LearningStyleRecommendations> {
    const prompt = `
Analyze learning preferences and provide personalized learning style recommendations:

Assessment Responses: ${JSON.stringify(responses, null, 2)}
Learning Priorities: ${JSON.stringify(insights.learningPriorities, null, 2)}

Provide learning style analysis in JSON format:
{
  "primaryLearningStyle": "Visual/Auditory/Kinesthetic/Reading",
  "recommendedFormats": ["format1", "format2", "format3"],
  "studySchedule": {
    "optimalSessionLength": "45-60 minutes",
    "frequencyPerWeek": 4,
    "bestTimeOfDay": "Morning",
    "breakIntervals": "15 minutes every hour",
    "reviewSchedule": "Weekly review sessions"
  },
  "motivationTechniques": ["technique1", "technique2"],
  "progressTrackingMethods": ["method1", "method2"],
  "socialLearningPreference": "Group/Individual/Mixed"
}
`;

    const result = await this.geminiService.generatePersonalizedContent(
      'learning_style_analysis',
      { responses, insights },
      {},
      userId
    );

    if (result.success && result.data) {
      return result.data as LearningStyleRecommendations;
    }

    // Fallback recommendations
    return {
      primaryLearningStyle: "Mixed learning approach",
      recommendedFormats: ["Online courses", "Hands-on projects", "Video tutorials"],
      studySchedule: {
        optimalSessionLength: "45-60 minutes",
        frequencyPerWeek: 3,
        bestTimeOfDay: "Evening",
        breakIntervals: "15 minutes every hour",
        reviewSchedule: "Weekly review sessions"
      },
      motivationTechniques: ["Goal setting", "Progress tracking", "Reward milestones"],
      progressTrackingMethods: ["Skill assessments", "Project completion", "Peer feedback"],
      socialLearningPreference: "Mixed individual and group learning"
    };
  }

  private async generateMarketTrendAnalysis(
    careerRecommendations: CareerPathRecommendation[],
    userId: string
  ): Promise<MarketTrendAnalysis> {
    const topCareer = careerRecommendations[0];
    if (!topCareer) {
      return this.generateFallbackMarketAnalysis();
    }

    const prompt = `
Provide current market trend analysis for the career path: ${topCareer.name}

Include analysis of:
- Industry growth trends
- Emerging and declining skills
- Salary trends
- Remote work opportunities
- Geographic opportunities
- Future outlook

Provide analysis in JSON format:
{
  "industryGrowth": "Growth analysis",
  "emergingSkills": ["skill1", "skill2", "skill3"],
  "decliningSkills": ["skill1", "skill2"],
  "salaryTrends": "Salary trend analysis",
  "remoteWorkOpportunities": "Remote work analysis",
  "geographicHotspots": ["location1", "location2"],
  "futureOutlook": "5-year outlook"
}
`;

    const result = await this.geminiService.generatePersonalizedContent(
      'market_trend_analysis',
      { careerPath: topCareer.name },
      {},
      userId
    );

    if (result.success && result.data) {
      return result.data as MarketTrendAnalysis;
    }

    return this.generateFallbackMarketAnalysis();
  }

  private calculatePersonalizationScore(
    responses: AssessmentResponse,
    insights: AssessmentInsights
  ): number {
    // Calculate based on response completeness and detail
    const responseCount = Object.keys(responses).length;
    const detailScore = responseCount >= 10 ? 100 : (responseCount / 10) * 100;
    const insightScore = insights.topSkills.length >= 3 ? 100 : (insights.topSkills.length / 3) * 100;
    
    return Math.round((detailScore + insightScore) / 2);
  }

  private calculateConfidenceLevel(
    personalityAnalysis: PersonalityAnalysis,
    careerFitAnalysis: CareerFitAnalysis[],
    skillGapInsights: SkillGapInsights
  ): number {
    // Calculate confidence based on data quality and consistency
    const personalityScore = personalityAnalysis.adaptabilityScore || 75;
    const careerFitScore = careerFitAnalysis[0]?.fitScore || 75;
    const skillDataQuality = skillGapInsights.criticalGaps.length > 0 ? 90 : 70;
    
    return Math.round((personalityScore + careerFitScore + skillDataQuality) / 3);
  }

  private generateFallbackInsights(): AIInsights {
    return {
      personalityAnalysis: {
        workStyle: "Collaborative and goal-oriented",
        motivationFactors: ["Growth", "Impact", "Recognition"],
        preferredEnvironment: "Dynamic workplace",
        communicationStyle: "Direct and clear",
        leadershipPotential: "Developing",
        stressManagement: "Proactive",
        adaptabilityScore: 75,
        teamworkPreference: "Balanced",
        innovationTendency: "Open to change",
        riskTolerance: "Moderate"
      },
      careerFitAnalysis: [
        {
          careerPath: "General Career Path",
          fitScore: 75,
          aiReasoning: "Based on your assessment responses, this represents a good general career direction with room for specialization.",
          personalityAlignment: ["Analytical thinking", "Problem-solving", "Communication"],
          potentialChallenges: ["Skill development needed", "Market competition"],
          successPredictors: ["Strong foundation", "Growth mindset", "Adaptability"],
          marketOutlook: "Positive growth expected in most sectors",
          salaryGrowthPotential: "Good advancement opportunities available",
          workLifeBalanceRating: 7,
          stressLevel: 5
        }
      ],
      skillGapInsights: this.generateFallbackSkillGapInsights(),
      learningStyleRecommendations: {
        primaryLearningStyle: "Mixed approach",
        recommendedFormats: ["Online courses", "Projects"],
        studySchedule: {
          optimalSessionLength: "45-60 minutes",
          frequencyPerWeek: 3,
          bestTimeOfDay: "Evening",
          breakIntervals: "15 minutes",
          reviewSchedule: "Weekly"
        },
        motivationTechniques: ["Goal setting"],
        progressTrackingMethods: ["Assessments"],
        socialLearningPreference: "Mixed"
      },
      marketTrendAnalysis: this.generateFallbackMarketAnalysis(),
      personalizationScore: 75,
      confidenceLevel: 75,
      generatedAt: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  private generateFallbackSkillGapInsights(): SkillGapInsights {
    return {
      criticalGaps: [],
      hiddenStrengths: ["Problem solving", "Communication"],
      transferableSkills: ["Project management", "Analysis"],
      learningPriority: [],
      timeToCompetency: {
        optimisticTimeline: "3-4 months",
        realisticTimeline: "6-8 months",
        conservativeTimeline: "10-12 months",
        factorsAffectingSpeed: ["Prior experience", "Time commitment"],
        accelerationOpportunities: ["Intensive courses", "Mentorship"]
      },
      alternativePathways: ["Related career paths"]
    };
  }

  private generateFallbackMarketAnalysis(): MarketTrendAnalysis {
    return {
      industryGrowth: "Steady growth expected",
      emergingSkills: ["Digital literacy", "Data analysis"],
      decliningSkills: ["Legacy systems"],
      salaryTrends: "Positive growth trajectory",
      remoteWorkOpportunities: "Increasing remote options",
      geographicHotspots: ["Major metropolitan areas"],
      futureOutlook: "Positive long-term prospects"
    };
  }
}

export const aiEnhancedAssessmentService = new AIEnhancedAssessmentService();
