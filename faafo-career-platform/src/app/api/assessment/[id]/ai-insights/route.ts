import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { aiEnhancedAssessmentService } from '@/lib/aiEnhancedAssessmentService';
import { EnhancedAssessmentService } from '@/lib/enhancedAssessmentService';
import { AssessmentResponse } from '@/lib/assessmentScoring';
import { cache } from '@/lib/cache';
import { withRateLimit, rateLimitConfigs, sanitizeInput } from '@/lib/rateLimit';
import { z } from 'zod';

// Input validation schema
const aiInsightsRequestSchema = z.object({
  assessmentId: z.string().uuid('Invalid assessment ID format'),
  focusAreas: z.array(z.string()).optional(),
  analysisDepth: z.enum(['basic', 'standard', 'comprehensive']).optional(),
  includeMarketData: z.boolean().optional(),
  personalityFocus: z.boolean().optional(),
});

// Rate limit configuration for AI insights
const aiInsightsRateLimit = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 AI insights generations per 15 minutes
  message: 'Too many AI insights requests. Please wait before generating more insights.',
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withRateLimit(request, aiInsightsRateLimit, async () => {
    try {
      const session = await getServerSession(authOptions);
      const { id: assessmentId } = await params;

      // Authentication check
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Input validation
      try {
        aiInsightsRequestSchema.parse({ assessmentId });
      } catch (validationError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: validationError instanceof z.ZodError ? validationError.errors : undefined
          },
          { status: 400 }
        );
      }

      // Check if AI services are available
      if (!process.env.GOOGLE_GEMINI_API_KEY) {
        return NextResponse.json(
          {
            success: false,
            error: 'AI services are temporarily unavailable',
            fallback: true,
            retryAfter: 300 // 5 minutes
          },
          { status: 503 }
        );
      }

      // Check cache first with improved error handling
      const cacheKey = `ai_insights:${assessmentId}:${session.user.id}:v1.0.0`;

      try {
        const cachedInsights = await cache.get(cacheKey);

        if (cachedInsights && typeof cachedInsights === 'string') {
          const parsed = JSON.parse(cachedInsights);

          // Validate cached data structure
          if (parsed && typeof parsed === 'object' && parsed.personalityAnalysis) {
            return NextResponse.json({
              success: true,
              data: parsed,
              cached: true,
              message: 'AI insights retrieved from cache',
              generatedAt: parsed.generatedAt || new Date().toISOString()
            });
          }
        }
      } catch (cacheError) {
        console.error('Cache retrieval error:', cacheError);
        // Continue to generate new insights if cache fails
      }

      // Verify assessment belongs to user with improved error handling
      const assessment = await prisma.assessment.findFirst({
        where: {
          id: assessmentId,
          userId: session.user.id
        },
        include: {
          responses: true,
          user: {
            select: {
              id: true,
              email: true,
              name: true
            }
          }
        }
      });

      if (!assessment) {
        return NextResponse.json(
          {
            success: false,
            error: 'Assessment not found or access denied',
            code: 'ASSESSMENT_NOT_FOUND'
          },
          { status: 404 }
        );
      }

      // Validate assessment has sufficient data
      if (!assessment.responses || assessment.responses.length < 3) {
        return NextResponse.json(
          {
            success: false,
            error: 'Insufficient assessment data for AI analysis',
            code: 'INSUFFICIENT_DATA',
            requiredResponses: 3,
            currentResponses: assessment.responses?.length || 0
          },
          { status: 400 }
        );
      }

    if (assessment.status !== 'COMPLETED') {
      return NextResponse.json(
        { success: false, error: 'Assessment is not completed' },
        { status: 400 }
      );
    }

    // Convert assessment responses to the expected format
    const responseData: AssessmentResponse = {};
    assessment.responses.forEach(response => {
      try {
        const value = typeof response.answerValue === 'string' 
          ? JSON.parse(response.answerValue) 
          : response.answerValue;
        responseData[response.questionKey] = value as string | string[] | number | null;
      } catch {
        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
      }
    });

    // Generate enhanced results first
    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
      assessmentId,
      responseData
    );

      // Generate AI insights with timeout and retry logic
      console.log(`Generating AI insights for user ${session.user.id}, assessment ${assessmentId}`);

      const startTime = Date.now();
      let aiInsights;

      try {
        // Set timeout for AI generation (5 minutes)
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('AI generation timeout')), 5 * 60 * 1000);
        });

        const aiGenerationPromise = aiEnhancedAssessmentService.generateAIInsights(
          assessmentId,
          responseData,
          enhancedResults.insights,
          enhancedResults.careerPathRecommendations,
          session.user.id
        );

        aiInsights = await Promise.race([aiGenerationPromise, timeoutPromise]);

      } catch (aiError) {
        console.error('AI insights generation error:', aiError);

        // Return fallback insights with error information
        return NextResponse.json(
          {
            success: false,
            error: 'AI insights generation failed',
            code: 'AI_GENERATION_FAILED',
            fallback: true,
            retryAfter: 60, // 1 minute
            details: aiError instanceof Error ? aiError.message : 'Unknown error'
          },
          { status: 500 }
        );
      }

      const generationTime = Date.now() - startTime;
      console.log(`AI insights generated in ${generationTime}ms for user ${session.user.id}, assessment ${assessmentId}`);

      // Cache the results for 24 hours with error handling
      try {
        await cache.set(cacheKey, JSON.stringify(aiInsights), 86400);
      } catch (cacheError) {
        console.error('Failed to cache AI insights:', cacheError);
        // Continue without caching
      }

      return NextResponse.json({
        success: true,
        data: aiInsights,
        cached: false,
        message: 'AI insights generated successfully',
        generationTime,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in AI insights generation:', error);

      // Provide detailed error information for debugging
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const errorCode = error instanceof Error && error.name ? error.name : 'UNKNOWN_ERROR';

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to generate AI insights',
          code: errorCode,
          details: process.env.NODE_ENV === 'development' ? errorMessage : undefined,
          timestamp: new Date().toISOString(),
          retryAfter: 60
        },
        { status: 500 }
      );
    }
  });
}

// POST endpoint to regenerate AI insights with specific focus areas
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: assessmentId } = await params;
    const body = await request.json();

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!process.env.GOOGLE_GEMINI_API_KEY) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'AI services are not configured' 
        },
        { status: 503 }
      );
    }

    const {
      focusAreas = [],
      analysisDepth = 'standard',
      includeMarketData = true,
      personalityFocus = true
    } = body;

    // Verify assessment belongs to user
    const assessment = await prisma.assessment.findFirst({
      where: {
        id: assessmentId,
        userId: session.user.id
      },
      include: {
        responses: true
      }
    });

    if (!assessment) {
      return NextResponse.json(
        { success: false, error: 'Assessment not found or access denied' },
        { status: 404 }
      );
    }

    // Convert assessment responses
    const responseData: AssessmentResponse = {};
    assessment.responses.forEach(response => {
      try {
        const value = typeof response.answerValue === 'string' 
          ? JSON.parse(response.answerValue) 
          : response.answerValue;
        responseData[response.questionKey] = value as string | string[] | number | null;
      } catch {
        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
      }
    });

    // Add user preferences to response data for AI analysis
    (responseData as any).ai_preferences = {
      focusAreas,
      analysisDepth,
      includeMarketData,
      personalityFocus
    };

    // Generate enhanced results with preferences
    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
      assessmentId,
      responseData
    );

    // Generate AI insights with custom focus
    const aiInsights = await aiEnhancedAssessmentService.generateAIInsights(
      assessmentId,
      responseData,
      enhancedResults.insights,
      enhancedResults.careerPathRecommendations,
      session.user.id
    );

    // Cache with custom key for preferences
    const customCacheKey = `ai_insights:${assessmentId}:${session.user.id}:custom:${Date.now()}`;
    await cache.set(customCacheKey, JSON.stringify(aiInsights), 3600); // 1 hour cache

    console.log(`Custom AI insights generated for user ${session.user.id}, assessment ${assessmentId}`);

    return NextResponse.json({
      success: true,
      data: aiInsights,
      cached: false,
      message: 'Custom AI insights generated successfully',
      preferences: {
        focusAreas,
        analysisDepth,
        includeMarketData,
        personalityFocus
      }
    });

  } catch (error) {
    console.error('Error generating custom AI insights:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate custom AI insights',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}

// DELETE endpoint to clear AI insights cache
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: assessmentId } = await params;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Clear cache for this assessment
    const cacheKey = `ai_insights:${assessmentId}:${session.user.id}`;
    await cache.del(cacheKey);

    console.log(`AI insights cache cleared for user ${session.user.id}, assessment ${assessmentId}`);

    return NextResponse.json({
      success: true,
      message: 'AI insights cache cleared successfully'
    });

  } catch (error) {
    console.error('Error clearing AI insights cache:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to clear AI insights cache'
      },
      { status: 500 }
    );
  }
}
